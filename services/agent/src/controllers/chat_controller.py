from __future__ import annotations

import json
from contextlib import asynccontextmanager
from typing import Any, Async<PERSON>enerator, Dict, List

from fastapi import APIRouter, HTTPException, FastAPI, Depends
from supertokens_python.recipe.session import SessionContainer
from supertokens_python.recipe.session.framework.fastapi import verify_session
from fastapi.responses import StreamingResponse, Response
from pydantic import BaseModel, Field

from src.controllers.base_controller import BaseController
from src.config import logger
from src.flows.chat_agent import chat_flow, ChatMessage, title_flow, ConversationInfoWithId
from src.utils.chat_utils.event_types import BaseEvent

# **** Request/Response Models
class ChatStreamRequest(BaseModel):
    """Request model for chat streaming endpoint."""
    conversation_id: str = Field(
        ...,
        description="Unique identifier for the conversation"
    )
    history: List[Dict[str, Any]] = Field(
        ...,
        description="List of message objects with role and content"
    )
    
    class Config:
        json_schema_extra = {
            "example": {
                "conversation_id": "conv_1234567890",
                "history": [
                    {"role": "user", "content": "Hello!"}
                ]
            }
        }

# **** Helper Functions
def to_chat_messages(history: List[Dict[str, Any]]) -> List[ChatMessage]:
    """Convert orchestrator message format to ChatMessage objects."""
    return [ChatMessage(role=m["role"], content=m["content"], extra=m.get("extra")) 
            for m in history]

def to_sse(event: Any) -> bytes:
    """Convert any object to Server-Sent Events format."""
    if hasattr(event, "model_dump_json"):
        payload = event.model_dump_json()
    else:
        payload = json.dumps(event)
    return f"data: {payload}\n\n".encode(encoding="utf-8")

@asynccontextmanager
async def lifespan_gen(gen: AsyncGenerator):
    """
    Context manager to ensure proper cleanup of async generators.
    
    Args:
        gen: Async generator to manage
        
    Yields:
        The generator itself
    """
    try:
        yield gen
    finally:
        if hasattr(gen, 'aclose'):
            await gen.aclose()

# **** Controller Implementation
class ChatController(BaseController):
    """Controller for handling chat interactions with the new event-based flow."""
    
    def __init__(self):
        super().__init__()
        self.router = APIRouter()
        logger.debug("ChatController initialized")

    def register_routes(self, app: FastAPI) -> None:
        """Register all chat-related routes."""
        self.router.add_api_route(
            "/chat/stream",
            self.chat_stream,
            methods=["POST"],
            response_class=StreamingResponse,
            response_model=None,
            tags=["chat"],
            summary="Stream chat responses",
            description="Stream chat responses using Server-Sent Events (SSE).",
            responses={
                200: {"description": "Stream of Server-Sent Events"},
                400: {"description": "Invalid request format"},
                500: {"description": "Internal server error"}
            }
        )
        
        self.router.add_api_route(
            "/chat/title",
            self.chat_title,
            methods=["POST"],
            response_class=Response,
            response_model=None,
            tags=["chat"],
            summary="Generate chat title and description",
            description="Generate a title and super condensed description for a conversation.",
            responses={
                200: {"description": "Title and description generated successfully"},
                400: {"description": "Invalid request format"},
                500: {"description": "Internal server error"}
            }
        )
        app.include_router(self.router, prefix="/v1")

    async def chat_stream(
        self,
        chat_request: ChatStreamRequest,
        session: SessionContainer = Depends(verify_session())
    ) -> StreamingResponse:
        """
        Handle streaming chat requests with Server-Sent Events (SSE).
        
        The endpoint streams back responses in real-time as they're generated.
        Each response is formatted as a Server-Sent Event.
        
        Args:
            chat_request: The chat request containing conversation history
            
        Returns:
            StreamingResponse: Configured for text/event-stream
        """
        try:
            # Use the already parsed chat_request
            request_data = chat_request

            logger.info(f"Starting chat stream for conversation: {request_data.conversation_id}")
            
            # Convert to chat messages and start the flow
            messages = to_chat_messages(request_data.history)
            
            # This creates the generator that will yield events from chat_flow
            # The generator is not started until we start iterating over it
            user_id = session.get_user_id()
            
            # This creates the generator that will yield events from chat_flow
            # The generator is not started until we start iterating over it
            event_gen: AsyncGenerator[BaseEvent, None] = chat_flow(
                messages, 
                conversation_id=request_data.conversation_id,
                user_id=user_id
            )

            async def event_stream() -> AsyncGenerator[bytes, None]:
                """Stream events as SSE-formatted bytes with proper cleanup."""
                # lifespan_gen ensures proper cleanup of the generator
                async with lifespan_gen(event_gen) as gen:
                    try:
                        # Process each event from the generator
                        async for event in gen:  # type: BaseEvent
                            logger.debug(f"Emitting event: {event.event_type}")
                            # Convert the event to SSE format and yield it
                            yield to_sse(event)
                        
                        # Signal stream completion
                        yield b"event: done\ndata: {}\n\n"
                        
                    except Exception as e:
                        logger.error(f"Error in event stream: {str(e)}")
                        # Send error to client before raising
                        yield to_sse({
                            "event": "error",
                            "message": "An error occurred while processing the stream"
                        })
                        raise  # This will be caught by the outer try/except

            # Return a StreamingResponse that will consume our event_stream generator
            return StreamingResponse(
                event_stream(),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "X-Content-Type-Options": "nosniff",  # Security header
                }
            )

        except Exception as e:
            logger.error(f"Unexpected error in chat_stream: {str(e)}")
            # For unhandled errors, we can't send a response anymore,
            # but we've already logged the error
            raise HTTPException(500, "Internal server error")

    async def chat_title(
        self,
        chat_request: ChatStreamRequest,
        session: SessionContainer = Depends(verify_session())
    ) -> Response:
        """Handle chat title generation."""
        try:
            user_id = session.get_user_id()
            messages = to_chat_messages(chat_request.history)
            response: ConversationInfoWithId = await title_flow(messages, chat_request.conversation_id, user_id)
            logger.info(f"Chat title response: {response}")
            json_response = json.dumps({
                "conversation_id": response.conversation_id,
                "short_title": response.short_title,
                "super_condense_description": response.super_condense_description
            })
            
            return Response(content=json_response, media_type="application/json")
        except (ValueError, KeyError) as e:
            logger.error(f"Error in chat_title: {str(e)}")
            raise HTTPException(400, "Invalid request format")
        except Exception as e:
            logger.error(f"Error in chat_title: {str(e)}")
            raise HTTPException(500, "Internal server error")
    
    
    async def lifespan_setup(self) -> None:
        """Initialize any resources needed by the controller."""
        logger.info("ChatController ready (no external resources to init)")

    async def lifespan_cleanup(self) -> None:
        """Clean up any resources used by the controller."""
        logger.info("ChatController cleaning up")