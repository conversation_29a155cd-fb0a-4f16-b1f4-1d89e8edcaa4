import httpx
import time

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request, Depends
from supertokens_python.recipe.session import <PERSON><PERSON><PERSON><PERSON>
from supertokens_python.recipe.session.framework.fastapi import verify_session
from slowapi import Limiter
from slowapi.util import get_remote_address

from src.controllers.base_controller import BaseController
from src.config import logger
# from src.flows.mail_flow import process_mail_thread_sequential
from src.utils.mail_utils.structs import MailThread  # QAAgentResponse
from src.flows import process_mail_thread, MailAgentResponse
__all__ = ["MailController"]
class MailController(BaseController):
  """
  Controller for mail-related endpoints that process customer emails
  and generate responses using the agentic system.
  """

  def __init__(self):
    # Initialize the base controller
    super().__init__()

    # Initialize the controller
    self.client = None
    self.limiter = Limiter(key_func=get_remote_address)

    logger.debug("Mail<PERSON>ontroller initialized")

  async def lifespan_setup(self) -> None:
    """
    Setup operations to be performed at application startup
    """
    self.client = httpx.AsyncClient()
    logger.info("MailController setup complete")

  async def lifespan_cleanup(self) -> None:
    """
    Cleanup operations to be performed at application shutdown
    """
    if self.client:
      await self.client.aclose()
    logger.info("MailController cleanup complete")

  async def get_client(self, request: Request) -> httpx.AsyncClient:
    """
    Dependency to provide the AsyncClient
    """
    return self.client

  async def process_mail_thread_handler(
    self, mail_thread: MailThread, request: Request, session: SessionContainer = Depends(verify_session())
  ) -> MailAgentResponse:
    """
    Handler for the /agent/mail endpoint that processes a mail thread
    """
    logger.info(f"Processing mail thread {mail_thread.id} with {len(mail_thread.mails)} emails")
    try:
      # Process the mail thread using the sequential flow
      start_time = time.time()
      user_id = session.get_user_id()
      response = await process_mail_thread(mail_thread, user_id)
      process_time = (time.time() - start_time) * 1000

      logger.info(f"Processed mail thread {mail_thread.id} in {process_time:.2f}ms")
      return response
    except Exception as e:
      logger.error(f"Error processing mail thread {mail_thread.id}: {str(e)}")
      raise HTTPException(status_code=500, detail=f"Error processing mail thread: {str(e)}")
  
  def register_routes(self, app: FastAPI) -> None:
    """
    Register all routes for this controller with the FastAPI app
    """
    # Set up rate limiter
    app.state.limiter = self.limiter

    @app.post("/agent/mail", response_model=MailAgentResponse)
    @self.limiter.limit("10/minute")  # Lower rate limit as this is a more resource-intensive endpoint
    async def process_mail(
      mail_thread: MailThread,
      request: Request,
      session: SessionContainer = Depends(verify_session()),
    ) -> MailAgentResponse:
      """
      Process a mail thread and generate a comprehensive response
      """
      return await self.process_mail_thread_handler(mail_thread, request, session)
