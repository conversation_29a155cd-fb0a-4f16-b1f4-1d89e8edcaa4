from typing import List, Dict, Any, Optional, Literal
import uuid
import httpx

from pydantic import BaseModel, Field
from agents import Agent
from src.utils.modelling import get_model
from src.config import settings, logger
from src.utils.mail_utils.structs import MailThread, QAPair, TopicLabel, QAAgentResponse, SearchResult



reasoning_model, tool_model = get_model("gpt-4o"), get_model("gpt-4o")
# reasoning_model, tool_model = small_model, small_model

# Define our context and data models
class MailAgentContext(BaseModel):
  mail_thread: Optional[MailThread] = None
  search_results: List[SearchResult] = Field(default_factory=list)
  qa_pairs: List[QAPair] = Field(default_factory=list)
  topic_labels: List[TopicLabel] = Field(default_factory=list)
  summary: Optional[str] = None
  reply: Optional[str] = None
  request_id: str = Field(default_factory=lambda: str(uuid.uuid4()))

  def add_search_results(self, results: List[Dict[str, Any]]) -> None:
    """Add search results to the context"""
    self.search_results.extend(results)

  def add_qa_pair(self, question: str, answer: str) -> None:
    """Add a QA pair to the context"""
    self.qa_pairs.append(QAPair(question=question, answer=answer))

  def add_topic_label(self, label: str, confidence: float = 1.0) -> None:
    """Add a topic label to the context"""
    self.topic_labels.append(TopicLabel(label=label, confidence=confidence))

  def set_summary(self, summary: str) -> None:
    """Set the summary of the mail thread"""
    self.summary = summary

  def set_reply(self, reply: str) -> None:
    """Set the reply to the mail thread"""
    self.reply = reply

  def get_final_response(self) -> QAAgentResponse:
    """Return the final response from the agent"""
    return QAAgentResponse(
      reply=self.reply or "",
      qa_pairs=self.qa_pairs,
      topic_labels=self.topic_labels,
      summary=self.summary or "",
      sources_contained_answer=len(self.search_results) > 0,
      sources=self.search_results,
    )


# @function_tool(name_override="search_index")
async def search_tool(query: str, user_id: str, collection: Literal["customer", "wikipedia"] = "customer", top_k: int = 5) -> List[SearchResult]:
  """Search for information in the knowledge base that might help answer customer questions.

  Args:
      query: The search query
      collection: The collection to search in (customer or wikipedia)
      top_k: Number of results to return (default: 5)
  """
  try:
    logger.debug(f"Searching {collection} for: {query}")
    async with httpx.AsyncClient() as client:
      response = await client.get(f"{settings.SEARCH_SERVICE_URL}/{collection}/search", params={"query": query, "user_id": user_id, "top_k": top_k})
      response.raise_for_status()
      results_json = response.json()

    # Convert the JSON results into SearchResult objects
    search_results = []
    for result in results_json:
      search_results.append(
        SearchResult(
          question=result.get("question", ""),
          answer=result.get("answer", ""),
          score=result.get("score", 0.0),
          underlying_scores=result.get("underlying_scores"),
        )
      )
    return search_results
  except Exception as e:
    logger.error(f"Error in search tool: {e}")
    return []


class SearchQuery(BaseModel):
  query: str


refine_search_query_agent = Agent(
  name="Refine Search Query Agent",
  instructions="""
    Reformulate the inquiry into a simple search statement that is used to search the knowledge base.
    """,
  model=tool_model,
  # tools=[search_tool],
  output_type=SearchQuery,
)

qa_agent = Agent(
  name="QA Extraction Agent",
  instructions="""
    you read the entire mail thread and an agent's latest reply to the mail thread.
    you need to extract the question-answer pairs from it.
    the question-answer pairs should be collectively exhaustive of the questions and answers in the mail thread.
    You should remain true to the original questions and answers found in the mail thread + last agent reply.
    These question / answer pairs are used to optimize the search query for the knowledge base!
    for example, if the mail thread is:
    ```
    Mail_from john: I have a question, and i was wondering if you can help me here: what is the capital of France?
    Mail_from support: Hi john! thanks for reaching out. The capital of France is Paris
    Mail_from john: And what about Germany i dont seem to know that either can you help me here too?
    Agent's latest reply: "Hi again, the capital of Germany is Berlin"
    ```
    then the question-answer pairs should be:
    ```
    Question: What is the capital of France?
    Answer: Hi John! thanks for reaching out. The capital of France is Paris
    Question: What is the capital of Germany?
    Answer: Hi again, the capital of Germany is Berlin
    ```
    """,
  model=tool_model,
  output_type=List[QAPair],
)


topics = [
  "Need Response",
  "FYI",
  "Promotion",
  "Marketing",
  "Order update",
  "Social media",
  "RSS feed"
]


labeling_agent = Agent(
  name="Topic Labeling Agent",
  instructions=f"""
    You are a specialized agent that identifies the main topics discussed in an email thread.
    The topics should be one or more of the following:
    {topics}
    """,
  model=tool_model,
  output_type=List[TopicLabel],
)


summary_agent = Agent(
  name="Summary Agent",
  instructions="""
    You are a specialized agent that generates concise, informative summaries of email threads.
    Focus on the key points, questions, and information exchanged.
    Your summary should be 2-3 sentences and capture the essence of the conversation.
    """,
  model=reasoning_model,
  output_type=str,
)

# reply_agent = Agent(
#   name="Reply Generation Agent",
#   instructions="""
#     You are a professional customer support agent. Generate a helpful, comprehensive reply 
#     to the customer's email. You will be given a mail thread and search results to help you answer the customer's questions.
    
#     1. Address all questions and concerns in the email
#     2. Be polite, professional, and concise
#     3. Use the search results to provide accurate information
#     4. Match the tone of the original email
#     5. End with an appropriate sign-off
    
#     You should respond with simple html formatting that is easy to render in a mail client.
#     write it like this:
    
#     example:
    
#     <html>
#       <body>
#         <p>Hello, how can I help you today?</p>
#       </body>
#     </html>
#     """,
#   model=reasoning_model,
#   output_type=str,
# )


reply_agent = Agent(
  name="Reply Generation Agent",
  instructions="""
    You are a professional mail service assistant. Your goal is to help users manage their email inbox smoothly.
    You will be given mail threads and have access to a search tool that can help you answer questions if needed.
    You will be given mail threads and have also access to a search tool that can help you answer questions if needed.
    
    
    Depending on the nature of the mail, you will need to:
    1. Address all questions and concerns in the email
    2. Be polite, professional, and concise
    3. Use the search results to provide accurate information
    4. Match the tone of the original email
    5. End with an appropriate sign-off
    
    If it is advertisement, newsletters, etc. simply reply "thanks for your email."
    
    You should respond with simple html formatting that is easy to render in a mail client.
    It should be in RAW html format, i.e. no triple backticks or <DOCTYPE> tags.
    
    example:
    
    <html>
      <body>
        <p>Hello, how can I help you today?</p>
      </body>
    </html>
    """,
  model=reasoning_model,
  output_type=str,
)