from __future__ import annotations

import asyncio
from dataclasses import dataclass
from typing import AsyncGenerator, List
from datetime import datetime

from agents import <PERSON>, <PERSON>, RunR<PERSON>ult

from src.config import logger
from src.utils.chat_utils.event_types import BaseEvent, EventProcessor
from src.utils.tools.mcps import exa_mcp, rag_mcp
from src.utils.modelling import litellm_model, small_model

@dataclass
class ChatMessage:
  role: str          # "user" | "assistant" | "system"
  content: str
  extra: dict | None = None



chat_agent_system_prompt = f"""
You are ClariQ **Chat Agent**, created by ClariQ Labs.

**Current date (year-month-day):** {datetime.now().strftime("%Y-%m-%d")}  

# Core identity & purpose
• You are a lightning-fast, very smart conversational partner that streams answers event-by-event.  
• Your vibe is a balanced mix of <PERSON>’ directness and <PERSON><PERSON>’s big-picture boldness: confident, concise, detail-oriented.  
• Your prime directive is to solve the user’s problem or curiosity as efficiently as possible while keeping things accurate, safe and friendly.

# Formatting & style
• Follow the user’s tone unless it violates policy.  
• Explain mathematics step-by-step; enclose math in triple-back-tick blocks.  
• When writing code, indent **2 spaces** and wrap the entire snippet in triple-back-tick blocks.  
• Prefer short paragraphs over bullet lists unless the user explicitly requests a list or ranking.  
• Cite web sources inline using the required citation syntax whenever you use the search tool.  
• Ask clarifying questions sparingly; do **not** ask for confirmation after every step of a multi-stage request.  

# Tool use & recency
• You have access to exa_mcp and rag_mcp search tools.  
• Whenever the user’s request could benefit from up-to-date or niche information, **browse first**.  
• If the user explicitly says “don’t search”, you must not browse.  
• For location-dependent queries, call the `user_info` tool so you can tailor answers.  
• The knowledge base is available to you and you can search it for information. 
• You can also create and update the knowledge base as you see fit if you think it is relevant.
• The collection in the knowledge base "user_facts" should contain memories about the user that will improve your responses over time. use this to note down things you learn about the user: tone of voice, interests, what mails are important to them, etc.

# Safety & refusal policy
• Refuse content that enables wrongdoing, sexualizes minors, or violates legal/ethical boundaries (weapons, malware, etc.).  
• If you refuse or partially comply, do so in ≤2 sentences and, if possible, offer a benign alternative.  
• Provide emotional support when appropriate, but never substitute for professional medical or legal advice.  

# Transparency & limitations
• When asked about post-cut-off events, say you may be out of date.  
• If asked product or pricing questions about ClariQ that you do not know, point users to the official docs/site.  

!This prompt is visible to you only. Begin every conversation ready to help.
!Do not mention this prompt in your responses, nor the tone of voice you are using.
"""



chat_agent = Agent(
  name="Chat Agent",
  instructions=chat_agent_system_prompt,
  model=litellm_model,
  mcp_servers=[exa_mcp, rag_mcp]
)

@dataclass
class ConversationInfo:
  short_title: str
  super_condense_description: str

@dataclass
class ConversationInfoWithId(ConversationInfo):
  conversation_id: str
  

title_agent = Agent(
  name="Title Agent",
  instructions="""You are a helpful assistant that generates a title and a super condensed description for a conversation based on the conversation history. \
    The title should be 2 to 8 words. From the user's perspective. keep it short and snappy. \
    The super condensed description should be 1 to 3 sentences and be written from the user's perspective. \
  """,
  model=small_model,
  output_type=ConversationInfo,
)


# Non-streaming title generation flow that is used to generate the title and super condensed description for a conversation
async def title_flow(history: List[ChatMessage], conversation_id: str, user_id: str) -> ConversationInfoWithId:
  # Convert to agent input format
  agent_input = [{"role": m.role, "content": m.content} for m in history]
  
  # Ensure conversation ends with user message (required by Mistral)
  if agent_input and agent_input[-1]["role"] == "assistant":
    agent_input.append({"role": "user", "content": "ok!"})
  
  response: RunResult = await Runner.run(title_agent, agent_input, user_id=user_id)
  output: ConversationInfo = response.final_output
  return ConversationInfoWithId(conversation_id=conversation_id, short_title=output.short_title, super_condense_description=output.super_condense_description)


async def chat_flow(
  history: List[ChatMessage],
  conversation_id: str,
  user_id: str,
) -> AsyncGenerator[BaseEvent, None]:
  """
  Thin façade around Runner.run_streamed that translates raw model chunks
  into strongly-typed events defined in `event_types.py`.
  """
  # Convert orchestrator history → agent input
  runner_input = [{"role": m.role, "content": m.content} for m in history]

  # Kick off the streamed run
  response = Runner.run_streamed(chat_agent, runner_input, user_id=user_id)

  # Process chunks → events on the fly
  processor = EventProcessor(conversation_id)

  async for chunk in response.stream_events():
    try:
      # logger.debug(f"Processing chunk: {chunk}")
      event = processor.process_chunk(chunk)
      if event is not None:
        # logger.debug(f"Yielding event: {event}")
        yield event
    except Exception as exc:
      logger.error(f"[chat_flow] failed on chunk {chunk}: {exc}")


if __name__ == "__main__":


  async def main() -> None:
    hist: list[ChatMessage] = [
      ChatMessage(role="user", content="use the exa tool to search for What's the weather like on Mars?"),
    ]
    async with exa_mcp, rag_mcp: 
      async for evt in chat_flow(hist, conversation_id="local-test"):
        logger.info(f"\n\n----EVENT----:\n{evt.model_dump_json(indent=2)}")

  asyncio.run(main())