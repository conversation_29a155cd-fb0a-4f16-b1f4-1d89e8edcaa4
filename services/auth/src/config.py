from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    # Google OAuth credentials
    GOOGLE_CLIENT_ID: str
    GOOGLE_CLIENT_SECRET: str
    
    # SuperTokens configuration
    SUPERTOKENS_CONNECTION_URI: str = "http://supertokens:3567"
    
    # App domains
    API_DOMAIN: str = "http://localhost:4000"
    WEBSITE_DOMAIN: str = "http://localhost:7777"
    
    model_config = SettingsConfigDict(env_file=".env")


settings = Settings()
