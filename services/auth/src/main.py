from supertokens_python import init, InputAppInfo, SupertokensConfig
from supertokens_python.recipe import thirdparty, session
from supertokens_python.recipe.thirdparty import SignInAndUpFeature, ProviderInput, ProviderConfig, ProviderClientConfig
from supertokens_python.framework.fastapi import get_middleware

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from .config import settings

# Initialize SuperTokens
init(
    app_info=InputAppInfo(
        app_name="ClariQ",
        api_domain=settings.API_DOMAIN,
        website_domain=settings.WEBSITE_DOMAIN,
        api_base_path="/auth"
    ),
    supertokens_config=SupertokensConfig(
        connection_uri=settings.SUPERTOKENS_CONNECTION_URI
    ),
    framework="fastapi",
    recipe_list=[
        thirdparty.init(
            sign_in_and_up_feature=SignInAndUpFeature(
                providers=[
                    ProviderInput(
                        config=ProviderConfig(
                            third_party_id="google",
                            clients=[
                                ProviderClientConfig(
                                    client_id=settings.GOOGLE_CLIENT_ID,
                                    client_secret=settings.GOOGLE_CLIENT_SECRET,
                                    scope=[
                                        "https://www.googleapis.com/auth/userinfo.email",
                                        "https://mail.google.com/"  # Full Gmail access
                                    ]
                                )
                            ]
                        )
                    )
                ]
            )
        ),
        session.init()
    ]
)

# Create FastAPI app
app = FastAPI(
    title="ClariQ Auth Service",
    description="Authentication service for ClariQ using SuperTokens",
    version="1.0.0"
)

# Add SuperTokens middleware first
app.add_middleware(get_middleware())

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[settings.WEBSITE_DOMAIN],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "auth"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
