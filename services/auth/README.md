# ClariQ Auth Service

This service handles authentication for the ClariQ application using SuperTokens with Google OAuth.

## Features

- Google OAuth integration with Gmail access scopes
- Session management via SuperTokens
- JWT token generation for service-to-service communication
- Health check endpoint

## Configuration

Environment variables (see `.env` file):

- `GOOGLE_CLIENT_ID`: Google OAuth client ID
- `GOOGLE_CLIENT_SECRET`: Google OAuth client secret
- `SUPERTOKENS_CONNECTION_URI`: SuperTokens core service URL
- `API_DOMAIN`: Auth service domain
- `WEBSITE_DOMAIN`: Frontend domain

## Endpoints

- `GET /health`: Health check
- `/auth/*`: SuperTokens authentication routes (handled by middleware)

## Dependencies

- SuperTokens core service (runs on port 3567)
- SuperTokens PostgreSQL database
- Google OAuth credentials

## Development

The service runs on port 4000 and is accessible at `http://localhost:4000`.

## Integration

The frontend is configured to use this service for authentication via the SuperTokens React SDK.
