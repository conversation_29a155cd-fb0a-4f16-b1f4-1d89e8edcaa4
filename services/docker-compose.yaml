networks:
  clariq:
    external: false

services:
  # cloudflared:
  #   image: cloudflare/cloudflared:latest
  #   container_name: cloudflared-tunnel
  #   command: tunnel --no-autoupdate run --token eyJhIjoiZTkyMzA1MDUwNDVkYTg4MjE2MzI0ZjM3Mzk2NGJjODAiLCJ0IjoiMjFiMmZkYzgtOGQyZC00YjNlLTlmZDYtN2ZiMzM2NDRiYWQxIiwicyI6Ik9HSmpPREUyWW1RdE1tRTRNaTAwWkRoaExXRTJaV1l0T1RkaFpHSTRNbVZqWm1OaCJ9
  #   depends_on:
  #     - frontend
  #   networks:
  #     - clariq

  # Production frontend (optimized build)
  # frontend:
  #   build:
  #     context: ..
  #     dockerfile: services/frontend/Dockerfile
  #   networks:
  #     - clariq
  #   env_file:
  #     - ./frontend/.env
  #   ports:
  #     - "7777:3000"
  #   depends_on:
  #     orchestrator:
  #       condition: service_healthy

  # Development frontend (hot reloading + real-time logs)
  frontend:
    build:
      context: ..
      dockerfile: services/frontend/Dockerfile.dev
    networks:
      - clariq
    env_file:
      - ./frontend/.env
    ports:
      - "7777:3000"  # Use standard Next.js port for dev
    volumes:
      # Mount source code for hot reloading
      - ../services/frontend/src:/app/src
      - ../services/frontend/public:/app/public
      - ../services/frontend/tailwind.config.js:/app/tailwind.config.js
      - ../services/frontend/next.config.ts:/app/next.config.ts
      - ../services/frontend/postcss.config.mjs:/app/postcss.config.mjs
      - ../services/frontend/tsconfig.json:/app/tsconfig.json
      # Exclude node_modules to avoid conflicts
      - /app/node_modules
    depends_on:
      orchestrator:
        condition: service_healthy
      auth:
        condition: service_started
    environment:
      - CHOKIDAR_USEPOLLING=true  # Enable file watching in Docker
      - WATCHPACK_POLLING=true    # Enable webpack polling

  # Authentication service
  auth:
    build:
      context: ..
      dockerfile: services/auth/Dockerfile
    networks:
      - clariq
    env_file:
      - ./auth/.env
    ports:
      - "4000:8000"
    depends_on:
      supertokens:
        condition: service_started

  agent:
    build:
      context: ..
      dockerfile: services/agent/Dockerfile
    networks:
      - clariq
    env_file:
      - ./agent/.env
    volumes:
      - ./agent/logs:/app/logs
    ports:
      - "8001:8000"
    depends_on:
      exa:
        condition: service_started
      llm_proxy:
        condition: service_started

  mail_connection:
    build:
      context: ..
      dockerfile: services/mail_connection/Dockerfile
    networks:
      - clariq
    env_file:
      - ./mail_connection/.env
    volumes:
      - ./mail_connection/logs:/app/logs
    ports:
      - "8002:8000"
    depends_on:
      postgres:
        condition: service_healthy
      pgweb:
        condition: service_started

  orchestrator:
    build:
      context: ..
      dockerfile: services/orchestrator/Dockerfile
    networks:
      - clariq
    env_file:
      - ./orchestrator/.env
    volumes:
      - ./orchestrator/logs:/app/logs
    ports:
      - "8003:8000"
    depends_on:
      postgres:
        condition: service_healthy
      pgweb:
        condition: service_started
      mail_connection:
        condition: service_started
      redis:
        condition: service_started
      qdrant:
        condition: service_started
      agent:
        condition: service_started

    healthcheck:
      test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8000/health')"]
      interval: 60s
      timeout: 10s
      retries: 3
      start_period: 20s

  # MCPs
  exa:
    build:
      context: ..
      dockerfile: services/mcps/exa/Dockerfile
    networks:
      - clariq
    ports:
      - "9001:9000"
    env_file:
      - ./mcps/exa/.env
  

  # SuperTokens database (separate from main app database)
  supertokens_db:
    image: postgres:17.4
    environment:
      POSTGRES_USER: supertokens
      POSTGRES_PASSWORD: supertokens_pass
      POSTGRES_DB: supertokens
    ports:
      - "5433:5432"  # Different port to avoid conflict
    volumes:
      - ../data/clariq/supertokens_pgdata:/var/lib/postgresql/data
    networks:
      - clariq
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U supertokens -d supertokens"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 10s

  # SuperTokens core service
  supertokens:
    image: registry.supertokens.io/supertokens/supertokens-postgresql
    depends_on:
      supertokens_db:
        condition: service_healthy
    ports:
      - "3567:3567"
    environment:
      POSTGRESQL_CONNECTION_URI: "*************************************************************/supertokens"
    networks:
      - clariq

  postgres:
    image: postgres:17.4
    environment:
      POSTGRES_USER: myuser
      POSTGRES_PASSWORD: mypass
      POSTGRES_DB: mydb
    ports:
      - "5432:5432"
    volumes:
      - ../data/clariq/pgdata:/var/lib/postgresql/data
    networks:
      - clariq
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U myuser -d mydb"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 10s

  pgweb:
    image: sosedoff/pgweb
    container_name: pgweb
    environment:
      - DATABASE_URL=**************************************/mydb?sslmode=disable
    networks:
      - clariq
    ports:
      - "8081:8081"
    depends_on:
      postgres:
        condition: service_healthy

  # PgWeb for SuperTokens database
  pgweb_supertokens:
    image: sosedoff/pgweb
    container_name: pgweb_supertokens
    environment:
      - DATABASE_URL=***********************************************************/supertokens?sslmode=disable
    networks:
      - clariq
    ports:
      - "8082:8081"  # Different port
    depends_on:
      supertokens_db:
        condition: service_healthy

  qdrant:
    image: qdrant/qdrant
    networks:
      - clariq
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - ../data/clariq/qdrant_storage:/qdrant/storage

  redis:
    image: redis/redis-stack
    networks:
      - clariq
    ports:
      - "6379:6379"
      - "8004:8001"  # Redis UI port
    volumes:
      - ../data/clariq/redis_data:/data

  llm_proxy:
    image: ghcr.io/berriai/litellm:main-latest
    networks:
      - clariq
    ports:
      - "8005:8000"
    env_file:
      - ./llm_proxy/.env
    volumes:
      - ./llm_proxy/litellm_config.yaml:/app/config.yaml
    command: ["--port", "8000", "--config", "/app/config.yaml"]

    restart: unless-stopped



