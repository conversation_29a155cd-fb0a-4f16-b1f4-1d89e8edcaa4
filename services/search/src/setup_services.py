from typing import List

from src.config import settings, logger
from src.base import Q<PERSON>air, SearchResult
from src.utils.index.qdrant_backend import QdrantIndexBackend
from src.utils.metastore.pg_store import PostgresMetadataStore
from src.semantic.generic_indexer import <PERSON><PERSON>Indexer, GenericSearcher
from src.semantic.encoder import TinyEncoder
from src.semantic.tinyconfig import tokenizer, get_model, model
from src.data import COMMON_TEST_DATA

pg_metadata_store = PostgresMetadataStore(host=settings.PG_HOST, port=settings.PG_PORT, user=settings.PG_USER, password=settings.PG_PASSWORD, dbname=settings.PG_DBNAME)

def create_backend(collection_name: str, embedding_dim: int = 384) -> QdrantIndexBackend:
  return QdrantIndexBackend(metadata_store=pg_metadata_store, host=settings.QDRANT_HOST, port=settings.QDRANT_PORT, api_key=settings.QDRANT_API_KEY).select_collection(collection_name, embedding_dim)

class Collection:
  def __init__(self, name: str, description: str, backend: QdrantIndexBackend, encoder: TinyEncoder):
    self.name, self.description = name, description
    self.searcher = GenericSearcher(encoder=encoder, backend=backend)
    self.indexer = GenericIndexer(encoder=encoder, backend=backend)
  
  def __repr__(self) -> str: return f"Collection({self.name})"
  def represent_collection(self) -> str: return f"** collection '{self.name}': {self.description} | Total items: {self.indexer.num_documents}"
  def index_data(self, data: List[QAPair], user_id: str) -> dict: self.indexer.index_data(data, user_id=user_id); return {"status": "success", "message": f"Indexed {len(data)} QAPairs into {self.name} for user {user_id}"}
  def search(self, query: str, top_k: int = 5, user_id: str = None) -> List[SearchResult]: return self.searcher.search(query, top_k, user_id=user_id, strategy="dot")
  def clean_index(self, user_id: str) -> dict: self.indexer.clean_index(user_id=user_id); return {"status": "success", "message": f"Cleaned index for {self.name} for user {user_id}"}

class CollectionCatalog:
  def __init__(self, encoder: TinyEncoder = None):
    self.encoder = encoder
    existing_names = QdrantIndexBackend(host=settings.QDRANT_HOST, port=settings.QDRANT_PORT, api_key=settings.QDRANT_API_KEY).get_all_collections()
    self.collections = [self._create_collection(name, pg_metadata_store.get_collection_description(name)) for name in existing_names]
    print(f"Collections: {self.collections}")
  
  def _create_collection(self, name: str, description: str, embedding_dim: int = 384) -> Collection: 
    return Collection(name=name, description=description, backend=create_backend(name, embedding_dim), encoder=self.encoder)
  
  def represent_collections(self) -> str: return f"## Collections:\n{chr(10).join(c.represent_collection() for c in self.collections)}"
  
  def create_collection(self, name: str, description: str, embedding_dim: int = 384) -> Collection:
    pg_metadata_store.register_collection(name, description)
    collection = self._create_collection(name, description, embedding_dim)
    self.collections.append(collection)
    return collection
  
  def get_collection(self, name: str) -> Collection | None: return next((c for c in self.collections if c.name == name), None)
  
  def delete_collection(self, name: str) -> None:
    self.collections = [c for c in self.collections if c.name != name]
    QdrantIndexBackend(host=settings.QDRANT_HOST, port=settings.QDRANT_PORT, api_key=settings.QDRANT_API_KEY).delete_collection(name)
    pg_metadata_store.delete_collection(name)
  
def initialize_catalog() -> CollectionCatalog:
  encoder = TinyEncoder(tokenizer, get_model(model), max_cache_size=15)
  return CollectionCatalog(encoder)

if __name__ == "__main__":
  catalog = initialize_catalog()
  collection = catalog.get_collection("wikipedia")
  catalog.delete_collection("wikipedia")
  collection.index_data(data=COMMON_TEST_DATA)
  
  # catalog.create_collection("general", "General Q&A pairs, this collection contains the most common questions and answers for the general", 384)
  print(catalog.represent_collections())
  # catalog.create_collection(name="customer", description="Customer Q&A pairs, this collection contains the most common questions and answers for the customer", embedding_dim=384)
  # print(catalog.represent_collections())
  # catalog.create_collection(name="wikipedia", description="Wikipedia Q&A pairs, this collection contains the most common questions and answers for the wikipedia", embedding_dim=384)
  # collection = catalog.get_collection("wikipedia")
  # collection.index_data(data=COMMON_TEST_DATA)
  
  
  
  
