import time
from typing import Callable, List

from fastapi import <PERSON>AP<PERSON>, Request, APIRouter, HTTPException, Query, Depends
from fastapi.middleware.cors import CORSMiddleware

from supertokens_python import init, InputAppInfo, SupertokensConfig
from supertokens_python.recipe import session
from supertokens_python.framework.fastapi import get_middleware
from supertokens_python.recipe.session.framework.fastapi import verify_session
from supertokens_python.recipe.session.interfaces import SessionContainer

init(
    app_info=InputAppInfo(
        app_name="ClariQ Search",
        api_domain="http://localhost:8000",
        website_domain="http://localhost:3001",
        api_base_path="/api/v1/auth",
    ),
    supertokens_config=SupertokensConfig(
        connection_uri="http://localhost:3567"
    ),
    framework="fastapi",
    recipe_list=[
        session.init()
    ]
)
from fastapi.middleware.gzip import GZipMiddleware

# Rate limiting
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

from src.setup_services import INDEXERS, SEARCHERS
from src.base import QAPair, SearchResult
from src.config import logger



# ***** FASTAPI APP *****
app = FastAPI(
  title="ClariQ Search API",
  version="0.1.0",
  contact={"name": "ClariQ Dev Team", "email": "<EMAIL>"},
  openapi_tags=[{"name": "Search", "description": "Multi-collection search operations"}],
)

# ***** MIDDLEWARE *****
app.add_middleware(get_middleware())
app.add_middleware(
  CORSMiddleware,
  allow_origins=["*"],
  allow_credentials=True,
  allow_methods=["*"],
  allow_headers=["*"],
)
app.add_middleware(GZipMiddleware, minimum_size=1000)

# Rate Limiter setup
limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)


# Custom Logging Middleware
@app.middleware("http")
async def log_requests(request: Request, call_next: Callable):
  """Custom logging middleware for request/response tracking"""
  start_time = time.time()
  logger.info(f"Request: {request.method} {request.url.path}")
  try:
    response = await call_next(request)
  except Exception as e:
    logger.error(f"Request error: {str(e)}")
    raise
  process_time = (time.time() - start_time) * 1000
  if response.status_code >= 400:
    logger.error(f"Response: {response.status_code} - {process_time:.2f}ms")
  else:
    logger.info(f"Response: {response.status_code} - {process_time:.2f}ms")
  return response


# ***** API ROUTES *****
router = APIRouter()


@router.post("/{collection}/index", response_model=dict, status_code=202)
async def index_data(
  collection: str,
  qa_pairs: List[QAPair],
  session: SessionContainer = Depends(verify_session())
):
  """
  Endpoint to index Q&A pairs into the specified collection asynchronously.
  E.g. POST /api/v1/customer/index with JSON body of QAPairs
  """
  user_id = session.get_user_id()
  if collection not in INDEXERS:
    raise HTTPException(status_code=400, detail=f"Unknown collection: {collection}")

  INDEXERS[collection].index_data(qa_pairs, user_id=user_id)

  return {"status": "accepted", "message": f"Indexing {len(qa_pairs)} Q&A pairs into '{collection}' in the background"}


@router.post("/{collection}/clean_index", response_model=dict)
async def clean_index(collection: str, session: SessionContainer = Depends(verify_session())):
  """
  Endpoint to clean/reset the specified collection.
  E.g. POST /api/v1/customer/clean_index
  """
  user_id = session.get_user_id()
  if collection not in INDEXERS:
    raise HTTPException(status_code=400, detail=f"Unknown collection: {collection}")

  try:
    INDEXERS[collection].clean_index(user_id=user_id)
    return {"status": "success", "message": f"Index '{collection}' cleaned"}
  except Exception as e:
    logger.error(f"Index cleaning failed: {str(e)}")
    raise HTTPException(status_code=500, detail=f"Index cleaning failed: {str(e)}")


@router.get("/{collection}/search", response_model=List[SearchResult])
@limiter.limit("300/minute")
async def search_query(collection: str, query: str = Query(..., min_length=1), top_k: int = Query(10, gt=0, le=100), session: SessionContainer = Depends(verify_session()), request: Request = None):
  """
  Search endpoint for a specific collection.
  E.g. GET /api/v1/customer/search?query=...

  - query must be at least 1 character
  - top_k defaults to 10, must be > 0 and <= 100
  """
  user_id = session.get_user_id()
  if collection not in SEARCHERS:
    raise HTTPException(status_code=400, detail=f"Unknown collection: {collection}")

  try:
    return SEARCHERS[collection].search(query=query, top_k=top_k, user_id=user_id)
  except Exception as e:
    logger.error(f"Search failed: {str(e)}")
    raise HTTPException(status_code=500, detail="Search failed")


@router.get("/health", include_in_schema=False)
async def health_check():
  """Simple health check endpoint."""
  return {"status": "ok"}


# Register our router with the FastAPI app
app.include_router(router, prefix="/api/v1", tags=["Search"])

if __name__ == "__main__":
  import uvicorn

  uvicorn.run(app, host="0.0.0.0", port=8000)
