import hashlib
from typing import List, Dict, Union

from src.config import logger
from src.base import BaseIndexer, BaseSearcher, IndexItem, QAPair, SearchResult, IndexBackend, BaseEncoder


class GenericIndexer(BaseIndexer):
  def __init__(self, encoder: BaseEncoder, backend: IndexBackend):
    self.encoder = encoder
    self.backend = backend

  def __call__(self, data: List[Union[IndexItem, QAPair]]) -> None:
    self.index_data(data)

  def index_data(self, data: List[Union[IndexItem, QAPair]], user_id: str, encode_answers: bool = False) -> None:
    if not data:
      logger.debug("No data to index; skipping.")
      return
    logger.debug("Indexing %d items", len(data))

    # Extract content for encoding - handle both IndexItem and QAPair
    content_texts = []
    additional_texts = []
    
    for item in data:
      if isinstance(item, QAPair):
        content_texts.append(item.question)  # Primary content for QAPair is the question
        if item.answer and encode_answers:
          additional_texts.append(item.answer)
      else:  # IndexItem
        content_texts.append(item.content)

    # Encode primary content
    content_embs = self.encoder(content_texts)
    additional_embs = self.encoder(additional_texts) if additional_texts else None

    new_ids = []
    new_meta: Dict[str, dict] = {}
    new_embeds = []
    additional_idx = 0

    for i, item in enumerate(data):
      # Ensure item has an ID
      item.ensure_id()
      
      # Generate content hash for versioning
      if isinstance(item, QAPair):
        content_for_hash = item.question + (item.answer or "")
      else:
        content_for_hash = item.content
      
      content_hash = hashlib.md5(content_for_hash.encode("utf-8")).hexdigest()

      # Combine embeddings
      combined_emb = content_embs[i]
      if isinstance(item, QAPair) and item.answer and encode_answers:
        combined_emb += additional_embs[additional_idx]
        additional_idx += 1
      combined_emb = combined_emb.realize()

      # Prepare metadata
      meta = item.model_dump()
      meta["version"] = 1
      meta["content_hash"] = content_hash
      meta["user_id"] = user_id

      new_ids.append(item.id)
      new_meta[item.id] = meta
      new_embeds.append(combined_emb)

    if not new_embeds:
      logger.debug("No new embeddings.")
      return

    # Stack embeddings
    if len(new_embeds) > 1:
      stacked = new_embeds[0].stack(*new_embeds[1:], dim=0)
    else:
      stacked = new_embeds[0]
      if len(stacked.shape) == 1:
        stacked = stacked.reshape((1, stacked.shape[0]))

    self.backend.upsert_documents(stacked, new_ids, new_meta)

  @property
  def num_documents(self) -> int:
    return self.backend.num_documents

  def clean_index(self, user_id: str) -> None:
    # if the backend supports cleaning
    if hasattr(self.backend, "clean_index"):
      self.backend.clean_index(user_id=user_id) # TODO: does not look like clean_index is registered


class GenericSearcher(BaseSearcher):
  def __init__(self, encoder: BaseEncoder, backend: IndexBackend):
    self.encoder = encoder
    self.backend = backend
    self._cached_version = -1

  def __call__(self, query: str, top_k: int = 5, user_id: str = None, strategy="dot"):
    return self.search(query, top_k, user_id, strategy)

  def search(self, query: str, top_k: int = 5, user_id: str = None, strategy="dot") -> List[SearchResult]:
    if isinstance(query, list):
      raise NotImplementedError("Batch search not implemented.")  # TODO: implement batch search

    if self.backend.num_documents == 0:
      logger.warning("Search => no documents.")
      return []
    encoded = self.encoder([query])
    if encoded.shape[0] == 0:
      logger.warning("Encoded query is empty. Returning no results.")
      return []

    q_emb = encoded[0].realize()
    results = self.backend.search(q_emb, top_k=top_k, user_id=user_id)

    out = []
    for doc_id, score_val in results:
      meta = self.backend.get_metadata(doc_id)
      out.append(SearchResult(**meta, score=score_val))
    return out
