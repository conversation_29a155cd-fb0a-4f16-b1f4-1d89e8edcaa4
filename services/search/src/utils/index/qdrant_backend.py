import uuid
from typing import List, Dict, Optional, Tuple

from tinygrad.tensor import Tensor

from src.config import settings, logger
from src.base import MetadataStore, IndexBackend

try:
  from qdrant_client import QdrantClient
  from qdrant_client.http import models as qmodels
  QDRANT_AVAILABLE = True
except ImportError:
  QDRANT_AVAILABLE = False


def make_qdrant_id(doc_id: str) -> str:
  """
  Generate a stable UUID from doc_id to avoid 'not a valid point ID' errors.
  For purely numeric doc_id, Qdrant tries to parse it as integer and can fail if negative, etc.
  We can ensure doc_id is recognized as a string by prefixing or using a stable UUID.
  """
  stable_uuid = str(uuid.uuid5(uuid.NAMESPACE_OID, doc_id))
  return stable_uuid


class QdrantIndexBackend(IndexBackend):
  def __init__(
    self,
    host: str = settings.QDRANT_HOST,
    port: int = settings.QDRANT_PORT,
    api_key: Optional[str] = settings.QDRANT_API_KEY,
    metadata_store: Optional[MetadataStore] = None,
  ):
    if not QDRANT_AVAILABLE:
      raise ImportError("QdrantClient is not installed.")

    self.metadata_store = metadata_store
    self.client = QdrantClient(url=(url := f"http://{host}:{port}"), api_key=api_key)
      
  def select_collection(self, collection_name: str, embedding_dim: int = 384) -> "QdrantIndexBackend":
    # Attempt to ensure collection
    try:
      self.client.get_collection(collection_name)
      logger.debug(f"Collection '{collection_name}' found.")
    except:
      logger.info(f"Creating collection '{collection_name}' with embedding_dim={embedding_dim}")
      self.client.recreate_collection(collection_name=collection_name, vectors_config=qmodels.VectorParams(size=embedding_dim, distance="Cosine"))
    
    self.collection_name = collection_name
    self.embedding_dim = embedding_dim
    
    return self
  
  def _ensure_collection(self):
    if self.collection_name is None:
      logger.error("No collection selected. Please select a collection using select_collection() first.")
      raise ValueError("No collection selected. Please select a collection using select_collection() first.")

  def upsert_documents(self, embeddings: Tensor, doc_ids: List[str], metadata: Dict[str, dict]) -> None:
    """
    Upsert each doc into Qdrant.
    - We generate a stable Qdrant ID for each doc_id to avoid 'invalid ID' errors.
    - We also store the original doc_id in the payload for easy cross-reference.
    """
    self._ensure_collection()
    
    emb_np = embeddings.numpy()
    points = []
    for i, doc_id in enumerate(doc_ids):
      vector_list = emb_np[i].tolist()
      payload = metadata.get(doc_id, {})

      # embed the original doc_id in the payload
      payload["original_doc_id"] = doc_id

      # generate a stable Qdrant ID
      qdrant_id = make_qdrant_id(doc_id)

      logger.info(f"Upserting doc {doc_id} => qdrant_id={qdrant_id}, embedding_dim={len(vector_list)}")

      point = qmodels.PointStruct(
        id=qdrant_id,  # Qdrant's internal ID (UUID string)
        vector=vector_list,  # the embedding
        payload=payload,  # any metadata you want indexed
      )
      points.append(point)

    # Upsert in one go
    logger.debug(f"Sending {len(points)} points to Qdrant...")
    self.client.upsert(collection_name=self.collection_name, points=points)

    # Also store metadata in Postgres, if desired
    if self.metadata_store:
      for did, meta in metadata.items():
        self.metadata_store.upsert(did, meta, self.collection_name)

  def update_embedding(self, idx: int, embedding: Tensor) -> None:
    """
    Qdrant doesn't allow in-place update by numeric index.
    You must re-upsert by Qdrant ID.
    So you'd need to track doc_id->Qdrant ID mapping if partial updates matter.
    """
    raise NotImplementedError("Use upsert_documents() again with the same doc_id => qdrant_id for partial updates.")

  def search(self, query_emb: Tensor, top_k: int = 5, user_id: str = None) -> List[Tuple[str, float]]:
    """
    Perform vector search in Qdrant.
    Convert the 1st row of query_emb to a list if there's more than one row.
    """
    self._ensure_collection()
    vec = query_emb.numpy()
    
    query_filter = None
    if user_id:
      query_filter = qmodels.Filter(
        must=[
          qmodels.FieldCondition(
            key="user_id",
            match=qmodels.MatchValue(value=user_id),
          )
        ]
      )

    hits = self.client.search(
      collection_name=self.collection_name, 
      query_vector=vec, 
      limit=top_k,
      query_filter=query_filter
    )
    results = []
    for hit in hits:
      doc_id = str(hit.id)  # Qdrant ID, i.e. the UUID
      score = float(hit.score)
      logger.debug(f"Found Qdrant doc_id={doc_id} with score={score:.4f}")
      results.append((doc_id, score))

    return results

  def get_metadata(self, doc_id: str) -> dict:
    """
    doc_id here is the Qdrant ID (the stable UUID).
    If we want to find the 'original' doc_id from Postgres,
    we either have a local doc_id->UUID mapping, or
    we stored 'original_doc_id' in payload for retrieval.
    """
    self._ensure_collection()
    recs = self.client.retrieve(collection_name=self.collection_name, ids=[doc_id])
    if not recs:
      return {}
    # The 'payload' includes all fields we stored, including 'original_doc_id'
    return recs[0].payload or {}

  def get_all_metadata(self) -> Dict[str, dict]:
    """
    Potentially expensive if you have a large Qdrant collection.
    If you're using Postgres as your main metadata store, you might prefer
    to read from Postgres directly.
    """
    if self.metadata_store:
      return self.metadata_store.load_all()

    logger.warning("get_all_metadata() from Qdrant might be expensive for large collections.")
    # There's no simple single-step method to retrieve everything from Qdrant in one call.
    return {}

  def get_doc_ids(self) -> List[str]:
    """
    Return a list of Qdrant IDs. If you have a Postgres store with original doc IDs,
    you might want to rely on that instead.
    """
    if self.metadata_store:
      return list(self.metadata_store.load_all().keys())
    return []

  @property
  def num_documents(self) -> int:
    self._ensure_collection()
    info = self.client.get_collection(self.collection_name)
    if info and info.status == "green":
      return info.points_count or 0
    return 0

  def clean_index(self, user_id: str) -> None:
    self._ensure_collection()
    logger.info(f"Cleaning index {self.collection_name} for user {user_id}")
    
    points_selector = qmodels.FilterSelector(
      filter=qmodels.Filter(
        must=[
          qmodels.FieldCondition(
            key="user_id",
            match=qmodels.MatchValue(value=user_id),
          )
        ]
      )
    )
    
    try:
      self.client.delete(
        collection_name=self.collection_name,
        points_selector=points_selector,
      )
      logger.info(f"Index {self.collection_name} cleaned successfully for user {user_id}")
    except Exception as e:
      logger.error(f"Error cleaning index {self.collection_name} for user {user_id}: {e}")

    if self.metadata_store:
      logger.warning(f"Metadata store for user {user_id} not cleaned. Implement user-specific cleaning if required.")
  
  def get_all_collections(self) -> List[str]:
    return [c.name for c in self.client.get_collections().collections]
  
