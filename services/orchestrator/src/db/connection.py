from typing import Generator, Optional
from contextlib import contextmanager

import psycopg2
from psycopg2.extensions import connection as psycopg2_connection
from psycopg2.pool import ThreadedConnectionPool
from psycopg2.extras import <PERSON><PERSON>

from src.config import settings, logger
from src.pydantic_models import Category, MailMetadata, QAPair

CREATE_TABLE_STATEMENTS: list[str] = [
    """
    CREATE TABLE IF NOT EXISTS orchestrator.users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        supertokens_id VARCHAR UNIQUE NOT NULL,
        email VARCHAR UNIQUE NOT NULL,
        full_name <PERSON><PERSON><PERSON><PERSON>,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
    );
    """,
    """
    CREATE TABLE IF NOT EXISTS orchestrator.categories (
      id SERIAL PRIMARY KEY,
      user_id UUID NOT NULL REFERENCES orchestrator.users(id) ON DELETE CASCADE,
      label TEXT NOT NULL,
      description TEXT,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
      UNIQUE(user_id, label)
    );
    """,
    """
    CREATE TABLE IF NOT EXISTS orchestrator.threads (
      thread_id VARCHAR PRIMARY KEY,
      user_id UUID NOT NULL REFERENCES orchestrator.users(id) ON DELETE CASCADE,
      mailbox_id VARCHAR,
      status TEXT DEFAULT 'active',
      processing_status TEXT DEFAULT 'active',
      error_message TEXT,
      summary TEXT,
      is_read BOOLEAN DEFAULT FALSE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
    );
    """,
    """
    CREATE TABLE IF NOT EXISTS orchestrator.mail_metadata (
      mail_id VARCHAR PRIMARY KEY,
      user_id UUID NOT NULL REFERENCES orchestrator.users(id) ON DELETE CASCADE,
      thread_id VARCHAR NOT NULL,
      mailbox_id VARCHAR NOT NULL,
      category_label TEXT,
      is_processed BOOLEAN DEFAULT FALSE,
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
      
      CONSTRAINT fk_thread
        FOREIGN KEY (thread_id)
        REFERENCES orchestrator.threads (thread_id)
    );
    """,
    """
    CREATE TABLE IF NOT EXISTS orchestrator.responses (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID NOT NULL REFERENCES orchestrator.users(id) ON DELETE CASCADE,
      thread_id VARCHAR NOT NULL,
      mailbox_id VARCHAR NOT NULL,
      mail_id VARCHAR,
      category_label TEXT,
      agent_type TEXT,
      agent_data JSONB,
      status TEXT NOT NULL DEFAULT 'pending',
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

      CONSTRAINT fk_thread
        FOREIGN KEY (thread_id)
        REFERENCES orchestrator.threads (thread_id)
    );
    """,
    """
    CREATE TABLE IF NOT EXISTS orchestrator.qa_pairs (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID NOT NULL REFERENCES orchestrator.users(id) ON DELETE CASCADE,
      response_id UUID NOT NULL,
      thread_id VARCHAR NOT NULL,
      question TEXT NOT NULL,
      answer TEXT,
      metadata JSONB,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

      CONSTRAINT fk_response
        FOREIGN KEY (response_id)
        REFERENCES orchestrator.responses (id) ON DELETE CASCADE,

      CONSTRAINT fk_thread
        FOREIGN KEY (thread_id)
        REFERENCES orchestrator.threads (thread_id)
    );
    """
]

# Index creation statements
CREATE_INDEXES: list[str] = [
    # User ID indexes
    "CREATE INDEX IF NOT EXISTS idx_users_supertokens_id ON orchestrator.users(supertokens_id);",
    "CREATE INDEX IF NOT EXISTS idx_categories_user_id ON orchestrator.categories(user_id);",
    "CREATE INDEX IF NOT EXISTS idx_threads_user_id ON orchestrator.threads(user_id);",
    "CREATE INDEX IF NOT EXISTS idx_mail_metadata_user_id ON orchestrator.mail_metadata(user_id);",
    "CREATE INDEX IF NOT EXISTS idx_responses_user_id ON orchestrator.responses(user_id);",
    "CREATE INDEX IF NOT EXISTS idx_qa_pairs_user_id ON orchestrator.qa_pairs(user_id);",

    # Existing indexes
    "CREATE INDEX IF NOT EXISTS idx_threads_mailbox ON orchestrator.threads(mailbox_id);",
    "CREATE INDEX IF NOT EXISTS idx_threads_status ON orchestrator.threads(status);",
    "CREATE INDEX IF NOT EXISTS idx_mail_metadata_thread ON orchestrator.mail_metadata(thread_id);",
    "CREATE INDEX IF NOT EXISTS idx_responses_thread ON orchestrator.responses(thread_id);",
    "CREATE INDEX IF NOT EXISTS idx_responses_mail ON orchestrator.responses(mail_id);",
    "CREATE INDEX IF NOT EXISTS idx_qa_pairs_response ON orchestrator.qa_pairs(response_id);",
    "CREATE INDEX IF NOT EXISTS idx_qa_pairs_thread ON orchestrator.qa_pairs(thread_id);",
    # Full-text search indexes for fast search
    "CREATE INDEX IF NOT EXISTS idx_mails_search ON mail_conn.mails USING GIN(to_tsvector('english', COALESCE(subject, '') || ' ' || COALESCE(body->>'html', '') || ' ' || COALESCE(from_email, '')));",
    "CREATE INDEX IF NOT EXISTS idx_responses_search ON orchestrator.responses USING GIN(to_tsvector('english', COALESCE(agent_data->>'reply', '') || ' ' || COALESCE(agent_data->>'summary', '')));",
    # Additional indexes for better search performance
    "CREATE INDEX IF NOT EXISTS idx_mails_subject ON mail_conn.mails USING GIN(to_tsvector('english', subject));",
    "CREATE INDEX IF NOT EXISTS idx_mails_from_email ON mail_conn.mails(from_email);",
    "CREATE INDEX IF NOT EXISTS idx_responses_status ON orchestrator.responses(status);"
]

# View creation statements
CREATE_VIEW_STATEMENTS: list[str] = [
    """
    CREATE OR REPLACE VIEW orchestrator.v_threads_unified AS
    WITH latest_response AS (
      SELECT
        id,
        user_id,
        thread_id,
        mailbox_id,
        mail_id,
        status,
        agent_type,
        created_at,
        updated_at,
        category_label,
        agent_data,
        ROW_NUMBER() OVER (PARTITION BY thread_id ORDER BY updated_at DESC) AS rn
      FROM orchestrator.responses
    ),
    all_threads AS (
      SELECT
        t.user_id,
        t.thread_id,
        t.mailbox_id,
        t.status,
        t.processing_status,
        t.error_message,
        t.summary,
        t.is_read,
        t.created_at,
        t.updated_at
      FROM orchestrator.threads t
      UNION ALL
      SELECT
        mt.user_id,
        mt.id AS thread_id,
        mt.mailbox_id,
        'active' AS status,
        'unprocessed' AS processing_status,
        NULL AS error_message,
        NULL AS summary,
        FALSE AS is_read,
        mt.last_updated AS created_at,
        mt.last_updated AS updated_at
      FROM mail_conn.mail_threads mt
      WHERE NOT EXISTS (
        SELECT 1 FROM orchestrator.threads t WHERE t.thread_id = mt.id
      )
    )
    SELECT
      -- User Info
      at.user_id,
      
      -- Thread core data
      at.thread_id,
      at.mailbox_id,
      at.status AS thread_status,
      at.processing_status,
      at.error_message,
      at.summary,
      at.is_read,
      at.created_at AS thread_created_at,
      at.updated_at AS thread_updated_at,
      
      -- Mail content (from ground truth - mail_conn)
      m.subject,
      m.from_email AS from_address,
      m.to_email AS to_addresses,
      m.body->>'html' AS body_html,
      m.timestamp AS mail_timestamp,
      
      -- AI enrichment (optional - NULL if no response)
      r.id AS response_id,
      r.status AS response_status,
      r.agent_type,
      r.created_at AS response_created_at,
      r.updated_at AS response_updated_at,
      r.category_label,
      r.agent_data->>'reply' AS ai_response,
      r.agent_data->>'summary' AS ai_summary,
      
      -- Topic labels (extracted from response agent_data)
      COALESCE(
        jsonb_agg(DISTINCT tl.label) FILTER (WHERE tl.label IS NOT NULL),
        '[]'::jsonb
      ) AS topic_labels,
      
      -- Computed fields
      GREATEST(at.updated_at, COALESCE(r.updated_at, at.updated_at)) AS last_updated
      
    FROM
      all_threads at
    LEFT JOIN
      mail_conn.mails m ON m.mail_thread_id = at.thread_id AND m.thread_position = 0
    LEFT JOIN
      latest_response r ON at.thread_id = r.thread_id AND r.rn = 1
    LEFT JOIN LATERAL 
      jsonb_to_recordset(r.agent_data -> 'topic_labels') AS tl(label text, confidence numeric) ON TRUE
    GROUP BY
      at.user_id, at.thread_id, at.mailbox_id, at.status, at.processing_status, at.error_message, at.summary,
      at.is_read, at.created_at, at.updated_at, m.subject, m.from_email, m.to_email, m.body, m.timestamp,
      r.id, r.status, r.agent_type, r.created_at, r.updated_at, r.category_label, r.agent_data
    ORDER BY 
      mail_timestamp DESC;
    """
]


# Global connection pool
_pool = None

def get_connection_pool(min_conn=1, max_conn=10, **kwargs):
  """Get or create the global connection pool"""
  global _pool
  if _pool is None:
    conn_params = {
      'host': kwargs.get('host', settings.PG_HOST),
      'port': kwargs.get('port', settings.PG_PORT),
      'user': kwargs.get('user', settings.PG_USER),
      'password': kwargs.get('password', settings.PG_PASSWORD),
      'dbname': kwargs.get('dbname', settings.PG_DBNAME)
    }
    try:
      _pool = ThreadedConnectionPool(min_conn, max_conn, **conn_params)
      logger.info(f"Created PostgreSQL connection pool with {min_conn}-{max_conn} connections to {conn_params['host']}:{conn_params['port']}")

      # Initialize schema on first connection
      conn = _pool.getconn()
      try:
        # Create schema outside of transaction to avoid conflicts
        conn.autocommit = True
        with conn.cursor() as cursor:
          try:
            cursor.execute("CREATE SCHEMA IF NOT EXISTS orchestrator;")
          except Exception as e:
            if "already exists" not in str(e):
              logger.error(f"Error creating schema: {e}")
              raise
        
        # Now create tables, indexes, and views in transaction
        conn.autocommit = False
        with conn.cursor() as cursor:
          cursor.execute("SET search_path TO orchestrator, public;")

          for q in CREATE_TABLE_STATEMENTS:
            cursor.execute(q)

          for idx_q in CREATE_INDEXES:
            try:
              cursor.execute(idx_q)
            except Exception as e:
              if "already exists" not in str(e):
                logger.error(f"Error creating index: {e}")
                raise
            
          # Create views after tables and indexes
          for view_q in CREATE_VIEW_STATEMENTS:
            cursor.execute(view_q)
            
          conn.commit()
        logger.info("Schema, indexes, and views initialized or verified.")
      finally:
        _pool.putconn(conn)
    except Exception as e:
      logger.error(f"Failed to create connection pool: {e}")
      _pool = None
      raise

  return _pool

class PostgresConnection:
  """
  Manages the PostgresSQL connection and provides a context manager for transactions.

  Orchestrator is responsible for maintaining these tables:
  * categories
  * mail_metadata
  * responses
  * qa_pairs
  * threads (if you also create them here)
  """

  def __init__(self, host=settings.PG_HOST, port=settings.PG_PORT, user=settings.PG_USER, password=settings.PG_PASSWORD, dbname=settings.PG_DBNAME):
    self.conn_params = dict(host=host, port=port, user=user, password=password, dbname=dbname)
    self.conn: Optional[psycopg2_connection] = None
    self.pool_conn = False

  def connect(self):
    """Establish connection and initialize schema if needed."""
    if self.conn:
      return

    try:
      # Try to get a connection from the pool first
      try:
        pool = get_connection_pool(**self.conn_params)
        self.conn = pool.getconn()
        self.pool_conn = True
        self.conn.autocommit = False
        logger.debug(f"Got connection from pool")
      except Exception as e:
        # Fall back to direct connection if pool fails
        logger.warning(f"Failed to get connection from pool: {e}, falling back to direct connection")
        self.conn = psycopg2.connect(**self.conn_params)
        self.pool_conn = False
        self.conn.autocommit = False
        self._init_schema()
        logger.info(f"Connected to PostgreSQL: {self.conn_params['dbname']}")
    except Exception as e:
      logger.error(f"Failed to connect to database: {e}")
      self.conn = None
      raise

  def close(self):
    """Return the connection to the pool or close it if it's a direct connection."""
    if self.conn:
      if self.pool_conn:
        # Return connection to the pool
        pool = get_connection_pool(**self.conn_params)
        pool.putconn(self.conn)
        logger.debug("Returned connection to pool")
      else:
        # Close direct connection
        self.conn.close()
        logger.info("Closed DB connection.")
      self.conn = None
  
  def _ensure_conn(self):
    # psycopg2 sets .closed to 0 when OK, non-zero when dead
    if not self.conn or self.conn.closed:
      if self.conn and self.pool_conn:
        # tell the pool this connection is bad
        pool = get_connection_pool(**self.conn_params)
        pool.putconn(self.conn, close=True)
      self.conn = None
      self.connect()

  @contextmanager
  def transaction(self, cursor_factory=None) -> Generator:
    """
    Transaction context manager. Any exception rolls back; otherwise commits.
    """
    self._ensure_conn()
    cursor = self.conn.cursor(cursor_factory=cursor_factory)
    try:
      yield cursor
      self.conn.commit()
    except Exception as e:
      self.conn.rollback()
      logger.error(f"Transaction failed: {e}")
      raise
    finally:
      cursor.close()

  def _init_schema(self):
    """Create necessary tables and indexes if they don't exist."""
    # Create schema outside transaction to avoid conflicts
    self.conn.autocommit = True
    with self.conn.cursor() as cur:
      try:
        cur.execute("CREATE SCHEMA IF NOT EXISTS orchestrator;")
      except Exception as e:
        if "already exists" not in str(e):
          logger.error(f"Error creating schema: {e}")
          raise
    
    # Reset autocommit and create tables/indexes in transaction
    self.conn.autocommit = False
    with self.transaction() as cur:
      cur.execute("SET search_path TO orchestrator, public;")   # Switch to that schema for subsequent table creation

      for q in CREATE_TABLE_STATEMENTS:
        cur.execute(q)

      for idx_q in CREATE_INDEXES:
        try:
          cur.execute(idx_q)
        except Exception as e:
          if "already exists" not in str(e):
            logger.error(f"Error creating index: {e}")
            raise
        
      # Create views after tables and indexes
      for view_q in CREATE_VIEW_STATEMENTS:
        cur.execute(view_q)
        
    logger.info("Schema, indexes, and views initialized or verified.")

  def store_category(self, category: Category, user_id: str) -> int:
    """
    Insert or get existing category by label. Returns category_id.
    """
    with self.transaction() as cur:
      # Check if category already exists for this user
      cur.execute("SELECT id FROM orchestrator.categories WHERE label=%s AND user_id=%s", (category.label, user_id))
      row = cur.fetchone()
      if row:
        return row[0]  # category_id

      # Otherwise, insert new
      cur.execute("""
        INSERT INTO orchestrator.categories (user_id, label, description)
        VALUES (%s, %s, %s)
        RETURNING id
      """, (user_id, category.label, category.description))
      new_id = cur.fetchone()[0]
      return new_id


  def store_mail_metadata(self, mail_metadata: MailMetadata):
    """
    Insert or update mail_metadata for the given mail_id,
    referencing the orchestrator.threads table by thread_id.
    """
    with self.transaction() as cur:
      cur.execute("""
        INSERT INTO orchestrator.mail_metadata (mail_id, user_id, thread_id, mailbox_id, category_label, is_processed)
        VALUES (%s, %s, %s, %s, %s, %s)
        ON CONFLICT (mail_id, user_id)
        DO UPDATE SET
          thread_id=EXCLUDED.thread_id,
          category_label=EXCLUDED.category_label,
          is_processed=EXCLUDED.is_processed,
          updated_at=NOW()
      """, (mail_metadata.mail_id, mail_metadata.user_id, mail_metadata.thread_id, mail_metadata.mailbox_id, mail_metadata.category_label, mail_metadata.is_processed))
      logger.debug(f"Stored/updated mail_metadata for mail {mail_metadata.mail_id}")


  def store_response(self, user_id: str, thread_id: str, mailbox_id: str, category_label: Optional[str] = None,
                   mail_id: Optional[str] = None, agent_type: str = "qa_agent",
                   agent_data: dict = None, status: str = "pending_review") -> str:
    """
    Insert a new row into orchestrator.responses with the given attributes.
    Returns the generated id (UUID).

    Args:
        thread_id: The ID of the thread this response belongs to
        mailbox_id: The ID of the mailbox
        category_label: Optional category label
        mail_id: Optional mail ID this response is associated with
        agent_type: Type of agent response (qa_agent, summary_agent, etc)
        agent_data: The JSON data containing the agent's response
        status: Status of the response (pending_review, approved, etc)

    Returns:
        str: The UUID of the inserted response
    """
    with self.transaction() as cur:
      # Ensure agent_data is properly converted to a JSON format PostgreSQL can handle
      if agent_data is not None:
        agent_data = Json(agent_data)

      cur.execute("""
        INSERT INTO orchestrator.responses
        (user_id, thread_id, mailbox_id, mail_id, category_label, agent_type, agent_data, status)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        RETURNING id
      """, (user_id, thread_id, mailbox_id, mail_id, category_label, agent_type, agent_data, status))
      new_id = cur.fetchone()[0]
      logger.debug(f"Stored response {new_id} for thread {thread_id}")
      return new_id


  def store_qa_pair(self, qa_pair: QAPair):
    """
    Inserts a single Q/A pair row into orchestrator.qa_pairs,
    referencing the orchestrator.responses row by response_id
    and the orchestrator.threads row by thread_id.
    """
    with self.transaction() as cur:
      # Convert metadata to JSON format
      metadata = Json(qa_pair.metadata) if qa_pair.metadata else Json({})

      cur.execute("""
        INSERT INTO orchestrator.qa_pairs
        (user_id, response_id, thread_id, question, answer, metadata)
        VALUES (%s, %s, %s, %s, %s, %s)
        RETURNING id
      """, (qa_pair.user_id, qa_pair.response_id, qa_pair.thread_id, qa_pair.question,
            qa_pair.answer, metadata))
      new_id = cur.fetchone()[0]
      logger.debug(f"Stored QA pair {new_id} for response {qa_pair.response_id}, thread {qa_pair.thread_id}")
      return new_id

  def ensure_thread_exists(self, user_id: str, thread_id: str, mailbox_id: Optional[str]):
    """
    Ensure the thread exists in orchestrator.threads, creating it if it does not exist.
    If mailbox_id is None, it will be fetched from mail_conn.mail_threads.
    """
    with self.transaction() as cur:
      # If mailbox_id is not provided, fetch it from mail_conn
      if mailbox_id is None:
        cur.execute("""
          SELECT mailbox_id FROM mail_conn.mail_threads
          WHERE id = %s
        """, (thread_id,))
        result = cur.fetchone()
        if result:
          mailbox_id = result[0]
        else:
          logger.warning(f"Thread {thread_id} not found in mail_conn.mail_threads")
          return  # Can't create thread without mailbox_id
      
      cur.execute("""
        INSERT INTO orchestrator.threads (thread_id, user_id, mailbox_id)
        VALUES (%s, %s, %s)
        ON CONFLICT (thread_id, user_id) DO NOTHING;
      """, (thread_id, user_id, mailbox_id))
      logger.debug(f"Ensured thread exists: {thread_id}")

  def update_thread_status(self, user_id: str, thread_id: str, processing_status: str, error_message: Optional[str] = None):
    """
    Update the processing status and optional error message for a thread.

    Args:
        thread_id: The ID of the thread to update
        processing_status: New status ('pending', 'processing', 'succeeded', 'failed')
        error_message: Optional error message if processing_status is 'failed'
    """
    with self.transaction() as cur:
      cur.execute("""
        UPDATE orchestrator.threads
        SET processing_status = %s,
            error_message = %s,
            updated_at = NOW()
        WHERE thread_id = %s AND user_id = %s
      """, (processing_status, error_message, thread_id, user_id))
      logger.debug(f"Updated thread {thread_id} status to {processing_status}")

  def get_thread_status(self, user_id: str, thread_id: str) -> dict:
    """
    Get the current status of a thread.

    Args:
        thread_id: The ID of the thread to check

    Returns:
        dict: Thread status information including processing_status and error_message
    """
    with self.transaction() as cur:
      cur.execute("""
        SELECT processing_status, error_message
        FROM orchestrator.threads
        WHERE thread_id = %s AND user_id = %s
      """, (thread_id, user_id))
      result = cur.fetchone()

      if result:
        processing_status, error_message = result
        return {
          "processing_status": processing_status,
          "error_message": error_message
        }
      return {"processing_status": "unknown", "error_message": "Thread not found"}

  def delete_existing_qa_pairs(self, user_id: str, thread_id: str) -> int:
    """
    Delete all existing QA pairs for a given thread.

    Args:
        thread_id: The ID of the thread whose QA pairs should be deleted

    Returns:
        int: Number of QA pairs deleted
    """
    with self.transaction() as cur:
      cur.execute("""
        DELETE FROM orchestrator.qa_pairs
        WHERE thread_id = %s AND user_id = %s
        RETURNING id
      """, (thread_id, user_id))

      # Count the number of deleted records
      deleted_records = cur.rowcount
      logger.debug(f"Deleted {deleted_records} existing QA pairs for thread {thread_id}")
      return deleted_records

  def delete_existing_responses(self, user_id: str, mail_id: str) -> int:
    """
    Delete all existing responses for a given mail_id.

    Args:
        mail_id: The ID of the mail whose responses should be deleted

    Returns:
        int: Number of responses deleted
    """
    # First retrieve the IDs of responses to be deleted
    response_ids = []
    with self.transaction() as cur:
      cur.execute("""
        SELECT id FROM orchestrator.responses
        WHERE mail_id = %s AND user_id = %s
      """, (mail_id, user_id))

      response_ids = [row[0] for row in cur.fetchall()]

    # Then delete QA pairs associated with these responses
    for response_id in response_ids:
      with self.transaction() as cur:
        cur.execute("""
          DELETE FROM orchestrator.qa_pairs
          WHERE response_id = %s
        """, (response_id,))
        deleted_qa_pairs = cur.rowcount
        logger.debug(f"Deleted {deleted_qa_pairs} QA pairs for response {response_id}")

    # Finally delete the responses themselves
    with self.transaction() as cur:
      cur.execute("""
        DELETE FROM orchestrator.responses
        WHERE mail_id = %s
        RETURNING id
      """, (mail_id,))

      # Count the number of deleted records
      deleted_records = cur.rowcount
      logger.debug(f"Deleted {deleted_records} existing responses for mail {mail_id}")
      return deleted_records
  def update_mail_processed_status(self, user_id: str, mail_id: str, is_processed: bool):
    """
    Update the is_processed status of a mail.

    Args:
        mail_id: The ID of the mail to update
        is_processed: Whether the mail has been fully processed
    """
    with self.transaction() as cur:
      cur.execute("""
        UPDATE orchestrator.mail_metadata
        SET is_processed = %s,
            updated_at = NOW()
        WHERE mail_id = %s AND user_id = %s
      """, (is_processed, mail_id, user_id))
      logger.debug(f"Updated mail {mail_id} processed status to {is_processed}")

  def search_mails(self, user_id: str, query: str, limit: int = 8) -> list[dict]:
    """
    Search mails using full-text search across subject, content, from_address, AI responses, and summaries.
    
    Args:
        query: Search query string
        limit: Maximum number of results to return
        
    Returns:
        List of mail search results with minimal data for display
    """
    if not query or len(query.strip()) < 2:
      return []
      
    search_query = query.strip().replace("'", "''")  # Escape single quotes
    
    with self.transaction() as cur:
      cur.execute("""
        WITH mail_search AS (
          SELECT 
            m.mail_thread_id as thread_id,
            m.subject,
            m.from_email as from_address,
            LEFT(m.body->>'html', 150) as message_snippet,
            m.timestamp,
            ts_rank(
              to_tsvector('english', COALESCE(m.subject, '') || ' ' || COALESCE(m.body->>'html', '') || ' ' || COALESCE(m.from_email, '')),
              plainto_tsquery('english', %s)
            ) as mail_rank
          FROM mail_conn.mails m
          WHERE m.user_id = %s AND
                to_tsvector('english', COALESCE(m.subject, '') || ' ' || COALESCE(m.body->>'html', '') || ' ' || COALESCE(m.from_email, ''))
                @@ plainto_tsquery('english', %s)
        ),
        response_search AS (
          SELECT 
            r.thread_id,
            r.status as response_status,
            r.updated_at as response_updated,
            LEFT(COALESCE(r.agent_data->>'reply', ''), 150) as ai_response_snippet,
            LEFT(COALESCE(r.agent_data->>'summary', ''), 150) as summary_snippet,
            ts_rank(
              to_tsvector('english', COALESCE(r.agent_data->>'reply', '') || ' ' || COALESCE(r.agent_data->>'summary', '')),
              plainto_tsquery('english', %s)
            ) as response_rank
          FROM orchestrator.responses r
          WHERE r.user_id = %s AND
                to_tsvector('english', COALESCE(r.agent_data->>'reply', '') || ' ' || COALESCE(r.agent_data->>'summary', ''))
                @@ plainto_tsquery('english', %s)
        )
        SELECT DISTINCT
          COALESCE(ms.thread_id, rs.thread_id) as thread_id,
          ms.subject,
          ms.from_address,
          ms.message_snippet,
          rs.ai_response_snippet,
          rs.summary_snippet,
          COALESCE(rs.response_status, 'pending_review') as response_status,
          GREATEST(ms.timestamp, rs.response_updated, ms.timestamp) as last_updated,
          GREATEST(COALESCE(ms.mail_rank, 0), COALESCE(rs.response_rank, 0)) as search_rank
        FROM mail_search ms
        FULL OUTER JOIN response_search rs ON ms.thread_id = rs.thread_id
        ORDER BY search_rank DESC, last_updated DESC
        LIMIT %s
      """, (user_id, search_query, user_id, search_query, user_id, search_query, user_id, search_query, limit))
      
      results = []
      for row in cur.fetchall():
        thread_id, subject, from_address, message_snippet, ai_response_snippet, summary_snippet, response_status, last_updated, search_rank = row
        results.append({
          'thread_id': thread_id,
          'subject': subject or 'No Subject',
          'from_address': from_address or '',
          'message_snippet': message_snippet or '',
          'ai_response_snippet': ai_response_snippet or '',
          'summary_snippet': summary_snippet or '',
          'response_status': response_status,
          'last_updated': last_updated.isoformat() if last_updated else '',
          'search_rank': float(search_rank) if search_rank else 0.0
        })
      
      logger.debug(f"Mail search for '{query}' returned {len(results)} results")
      return results
