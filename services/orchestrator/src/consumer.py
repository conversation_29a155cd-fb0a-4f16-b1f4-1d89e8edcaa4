import sys
import signal
import time
from psycopg2.extras import RealDictCursor
import redis
import socket

from src.db.connection import PostgresConnection, get_connection_pool
from src.logic import Orchestrator
from src.pydantic_models import MailThread
from src.pydantic_models import Mail
from src.config import settings, logger

# Initialize Redis client with decode_responses=True to automatically decode byte responses to strings
redis_client = redis.Redis(
  host=settings.REDIS_HOST, 
  port=settings.REDIS_PORT, 
  decode_responses=True,
  health_check_interval=30,  # Periodically check connection health
  socket_keepalive=True,     # Keep socket alive
  socket_timeout=30          # Socket timeout
)

# Log Redis pool stats every 5 minutes
last_stats_time = time.time()
stats_interval = 300  # 5 minutes

def log_redis_stats():
  global last_stats_time
  current_time = time.time()
  if current_time - last_stats_time >= stats_interval:
    try:
      # Get Redis info
      info = redis_client.info()
      clients_info = redis_client.info('clients')
      
      # Log connection stats
      logger.info(f"Redis stats - connected_clients: {clients_info.get('connected_clients', 'N/A')}, "
                 f"blocked_clients: {clients_info.get('blocked_clients', 'N/A')}, "
                 f"tracking_clients: {clients_info.get('tracking_clients', 'N/A')}")
      
      last_stats_time = current_time
    except Exception as e:
      logger.error(f"Error getting Redis stats: {e}")

# Initialize the connection pool
get_connection_pool(min_conn=1, max_conn=5)

# Get a connection from the pool
db_conn = PostgresConnection()
db_conn.connect()

# Initialize orchestrator with the connection
orchestrator = Orchestrator(db_conn)

def consume_events():
  # Create a unique consumer name based on hostname
  hostname = socket.gethostname()
  consumer_name = f"orch-{hostname}"
  group_name = "mailcg"
  
  # We only need to process the new_mail stream
  stream_name = "mail_events:new_mail"
  
  try:
    # Create the consumer group if it doesn't exist
    # The '0' starting ID means "from the beginning of the stream"
    redis_client.xgroup_create(stream_name, group_name, id='0', mkstream=True)
    logger.info(f"Created consumer group '{group_name}' for stream '{stream_name}'")
  except redis.exceptions.ResponseError as e:
    if "BUSYGROUP" in str(e):
      # Group already exists, which is fine
      logger.info(f"Consumer group '{group_name}' already exists for stream '{stream_name}'")
    else:
      # Some other error occurred
      logger.error(f"Error creating consumer group: {e}")
      raise

  logger.info(f"Orchestrator event consumer started as '{consumer_name}' in group '{group_name}'... 🚀")
  
  while True:
    try:
      # Log Redis stats periodically
      log_redis_stats()
      
      # 1. First, check for any pending messages that might have been delivered but not processed
      # Redis 7 returns 3-tuple: next_id, entries, deleted
      result = redis_client.xautoclaim(
        stream_name,           # stream
        group_name,            # consumer‑group
        consumer_name,         # this consumer
        min_idle_time=10000,   # 30s - longer time before reclaiming for GPU processing
        start_id='0-0',        # from the beginning
        count=1                # Process only 1 pending message at a time (GPU-friendly)
      )

      # Handle differences between Redis versions
      claimed = []
      if len(result) == 3:
        _next_start_id, claimed, _deleted = result
      else:
        _next_start_id, claimed = result

      # Process any reclaimed message
      if claimed:
        event_id, data = claimed[0]
        process_mail_event(stream_name, group_name, event_id, data)
        # Add a small delay to prevent overwhelming the GPU
        time.sleep(1)
        continue  # Continue to process next message
      
      # 2. If no pending messages, read a new message with XREADGROUP
      # The '>' special ID means "give me only new messages that were never delivered to any consumer"
      messages = redis_client.xreadgroup(
        groupname=group_name,
        consumername=consumer_name,
        streams={stream_name: '>'},
        count=1,               # Process only 1 message at a time (GPU-friendly)
        block=5000             # Block for 5 seconds if no messages are available
      )
      
      if not messages:
        # No new messages, continue the loop
        continue
        
      # Process the new message
      for stream_data in messages:
        stream_key, events = stream_data
        if events:
          event_id, data = events[0]
          process_mail_event(stream_key, group_name, event_id, data)
          # Add a small delay to prevent overwhelming the GPU
          time.sleep(0.01)

    except redis.RedisError as e:
      logger.error(f"Redis error: {e}", exc_info=True)
      time.sleep(5)  # backoff on Redis errors
    except Exception as e:
      logger.error(f"Unexpected error in consumer loop: {e}", exc_info=True)
      time.sleep(5)  # backoff on unexpected errors


def process_mail_event(stream_name: str, group_name: str, event_id: str, data: dict):
  """
  Process a mail event from the new_mail stream.
  Fetches the thread, runs orchestration, and ACKs the message on success.
  """
  thread_id = data.get("thread_id")
  mail_id = data.get("mail_id")
  user_id = data.get("user_id")

  if not thread_id or not user_id:
    logger.error(f"❌ Missing thread_id or user_id in event data: {data}")
    redis_client.xack(stream_name, group_name, event_id)
    return

  logger.info(f"📨 Processing mail event: mail_id={mail_id}, thread_id={thread_id}, user_id={user_id}")

  try:
    thread = fetch_thread_via_db(thread_id, user_id)
    if thread is None:
      logger.warning(f"Thread {thread_id} for user {user_id} not found when processing event with mail_id={mail_id}. Acknowledging and skipping.")
      redis_client.xack(stream_name, group_name, event_id)
      return

    orchestrator.orchestrate_thread(thread)

    logger.info(f"✅ Successfully processed mail {mail_id} in thread {thread_id} for user {user_id}")

    redis_client.xack(stream_name, group_name, event_id)
  except Exception as e:
    logger.error(f"❌ Error processing thread {thread_id} for user {user_id}: {e}", exc_info=True)


# Fetch thread and mails directly from Postgres, not via HTTP API
def fetch_thread_via_db(thread_id: str, user_id: str) -> Optional[MailThread]:
  """
  Fetch the full MailThread for a specific user directly from Postgres.
  """
  with db_conn.transaction(cursor_factory=RealDictCursor) as cur:
    cur.execute("""
      SELECT id, mailbox_id, user_id
      FROM mail_conn.mail_threads
      WHERE id = %s AND user_id = %s
    """, (thread_id, user_id))
    thread_row = cur.fetchone()
    if not thread_row:
      return None

    cur.execute("""
      SELECT id, mail_thread_id, mailbox_id, from_email, to_email, subject, body, timestamp, thread_position, received_or_sent, user_id
      FROM mail_conn.mails
      WHERE mail_thread_id = %s AND user_id = %s
      ORDER BY thread_position ASC
    """, (thread_id, user_id))
    
    mail_rows = cur.fetchall()
    mails = [Mail(**row) for row in mail_rows]

  return MailThread(
    id=thread_row["id"],
    mailbox_id=thread_row["mailbox_id"],
    user_id=thread_row["user_id"],
    mails=mails
  )

def handle_signal(sig, frame):
  logger.info(f"Signal {sig} received. Stopping service.")
  sys.exit(0)

if __name__ == "__main__":
  signal.signal(signal.SIGINT, handle_signal)
  signal.signal(signal.SIGTERM, handle_signal)

  try:
    consume_events()
  except KeyboardInterrupt:
    logger.info("KeyboardInterrupt received, shutting down consumer...")
    sys.exit(0)
  except Exception as e:
    logger.error(f"Exception in consumer: {e}", exc_info=True)
    sys.exit(1)  # Exit with error code on exceptions