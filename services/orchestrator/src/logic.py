import requests
import json
import redis
import time
from datetime import datetime

from src.services.agent_client import Agent<PERSON>lient

from src.db.connection import PostgresConnection
from src.config import settings, logger
from src.pydantic_models import Mail, MailThread, MailMetadata, AgentResponse, QAPair, QAAgentResponse
from src.constants import ORCH_STREAM

# Create Redis client for publishing events
redis_client = redis.Redis(
  host=settings.REDIS_HOST,
  port=settings.REDIS_PORT,
  decode_responses=True
)


class Orchestrator:
  def __init__(self, db_connection: PostgresConnection):
    self.db_conn = db_connection
    self.redis_client = redis_client  # Use the module-level instance
    self.agent_client = AgentClient(settings.AGENT_SERVICE_URL, rate=10, per=60)

  def publish_frontend_event(self, event_type: str, user_id: str, data: dict):
    """Publish a user-specific event to the frontend_events Redis channel."""
    try:
      event_data = {
        "event_type": event_type,
        "user_id": user_id,
        **data,
        "timestamp": datetime.now().isoformat()
      }
      # Channel is now user-specific
      channel = f"frontend_events:{user_id}"
      redis_client.publish(channel, json.dumps(event_data))
      logger.info(f"Published {event_type} event to {channel} channel for user {user_id}")
    except Exception as e:
      logger.error(f"Error publishing {event_type} for user {user_id}: {e}")

  def _publish_thread_processed_event(self, thread_id: str, mail_id: str):
    """
    Publish a thread_processed event to the Redis stream.
    This is more durable than direct PubSub and reduces connections.
    """
    try:
      # Use the same Redis client from the class
      self.redis_client.xadd(
        ORCH_STREAM,
        {
          "event": "thread_processed",
          "thread_id": thread_id,
          "mail_id": mail_id,
          "ts": int(time.time())
        },
        maxlen=10000,  # cap to keep Redis small (ring-buffer style)
        approximate=True
      )
      logger.info(f"Published thread_processed event to stream for thread {thread_id}")
    except Exception as e:
      logger.error(f"Error publishing thread_processed event: {e}", exc_info=True)

  def orchestrate_thread(self, mail_thread: MailThread):
    """
    Orchestrates the processing of a mail thread for a specific user.
    """
    user_id = mail_thread.user_id
    logger.info(f"Orchestrating thread {mail_thread.id} for user {user_id} with {len(mail_thread.mails)} mails.")

    thread_status = self.db_conn.get_thread_status(mail_thread.id, user_id)
    if thread_status and thread_status["processing_status"] == "succeeded":
      logger.info(f"Thread {mail_thread.id} for user {user_id} already successfully processed, skipping.")
      return

    self.db_conn.ensure_thread_exists(mail_thread.id, mail_thread.mailbox_id, user_id)
    self.db_conn.update_thread_status(mail_thread.id, "processing", user_id=user_id)

    try:
      mail_thread.mails = sorted(mail_thread.mails, key=lambda x: x.thread_position)
      category_label = None
      mails_to_mark_processed = []

      for mail in mail_thread.mails:
        logger.info(f"Processing mail {mail.id} in thread {mail_thread.id} for user {user_id}")

        mail_metadata = self._get_mail_metadata(mail.id, user_id)
        if mail_metadata and mail_metadata.is_processed:
          logger.info(f"Mail {mail.id} for user {user_id} already processed, skipping.")
          category_label = mail_metadata.category_label
          continue

        category_label = self._categorize_mail(mail_thread, mail)

        mail_metadata = MailMetadata(
          mail_id=mail.id,
          thread_id=mail_thread.id,
          mailbox_id=mail.mailbox_id,
          user_id=user_id,
          category_label=category_label,
          is_processed=False
        )
        self.db_conn.store_mail_metadata(mail_metadata)
        mails_to_mark_processed.append(mail.id)

      received_or_sent = mail_thread.mails[-1].received_or_sent
      if received_or_sent == "received" and category_label:
        qa_agent_response = self._call_qa_agent(mail_thread)

        agent_response = AgentResponse.from_qa_agent_response(
          qa_response=qa_agent_response,
          thread_id=mail_thread.id,
          mailbox_id=mail_thread.mailbox_id,
          user_id=user_id,
          mail_id=mail_thread.mails[-1].id,
          category_label=category_label
        )

        agent_data_dict = agent_response.agent_data
        if isinstance(agent_data_dict, QAAgentResponse):
          agent_data_dict = agent_data_dict.model_dump(mode='json')

        if mail_thread.mails[-1].id:
          self.db_conn.delete_existing_responses(mail_thread.mails[-1].id, user_id)

        response_id = self.db_conn.store_response(
          thread_id=agent_response.thread_id,
          mailbox_id=agent_response.mailbox_id,
          user_id=user_id,
          mail_id=agent_response.mail_id,
          category_label=agent_response.category_label,
          agent_type=agent_response.agent_type,
          agent_data=agent_data_dict,
          status=agent_response.status
        )

        self.publish_frontend_event("response_created", user_id, {
          "response_id": response_id,
          "thread_id": agent_response.thread_id,
          "mail_id": agent_response.mail_id,
          "status": agent_response.status
        })

        self.db_conn.delete_existing_qa_pairs(agent_response.thread_id, user_id)

        for qa in qa_agent_response.qa_pairs:
          qa_pair = QAPair(
            response_id=response_id,
            thread_id=agent_response.thread_id,
            question=qa.question,
            answer=qa.answer,
            metadata={}
          )
          self.db_conn.store_qa_pair(qa_pair, user_id)
          logger.info(f"Stored QA pair for response {response_id} for user {user_id}.")

      for mail_id in mails_to_mark_processed:
        self.db_conn.update_mail_processed_status(mail_id, True, user_id)

      self.db_conn.update_thread_status(mail_thread.id, "succeeded", user_id=user_id)
      logger.info(f"Done orchestrating thread {mail_thread.id} for user {user_id}.")

      self._publish_thread_processed_event(str(mail_thread.id), str(mail_thread.mails[-1].id))

    except Exception as e:
      error_message = f"Error processing thread for user {user_id}: {str(e)}"
      logger.error(error_message, exc_info=True)
      self.db_conn.update_thread_status(mail_thread.id, "failed", error_message, user_id=user_id)

      self.publish_frontend_event("thread_processing_failed", user_id, {
        "thread_id": mail_thread.id,
        "status": "failed",
        "error": str(e)
      })

      raise

  def _categorize_mail(self, mail_thread: MailThread, mail: Mail):
    """Leverages the mailthread and the mail to categorize the mail."""
    # TODO: implement this
    return "wikipedia"

  def _call_qa_agent(self, mail_thread: MailThread) -> QAAgentResponse:
    """Calls the QA agent with the mailthread and returns the response.

    Args:
        mail_thread: The mail thread to process
        qa_pairs: Optional list of existing QA pairs to provide context

    Returns:
        QAAgentResponse: The parsed response from the agent service
    """
    try:
      # Call the agent service mail endpoint
      logger.info(f"Calling agent service for mail thread {mail_thread.id}")

      # Convert Pydantic model to dict for the request, with custom handling for datetime objects
      thread_data = mail_thread.model_dump(mode='json')

      # Make request to the agent service with rate limiting
      response = self.agent_client.post_mail(thread_data)

      # Raise an exception for HTTP error status
      response.raise_for_status()

      # Parse the response data as a QAAgentResponse
      response_data = response.json()
      agent_response = QAAgentResponse(**response_data)
      logger.info(f"Received agent response for mail thread {mail_thread.id}")

      return agent_response
    except Exception as e:
      logger.error(f"Error calling QA agent for mail thread {mail_thread.id}: {str(e)}")
      # Return a minimal placeholder response
      return QAAgentResponse(
        reply="We are experiencing technical difficulties. Please try again later.",
        qa_pairs=[],
        topic_labels=[],
        summary="Technical difficulties encountered",
        sources_contained_answer=False
      )

  def _get_mail_metadata(self, mail_id: str, user_id: str) -> MailMetadata | None:
    sql = """
      SELECT mail_id, thread_id, mailbox_id, user_id, category_label, is_processed
      FROM orchestrator.mail_metadata
      WHERE mail_id = %s AND user_id = %s
    """

    with self.db_conn.transaction() as cursor:
      cursor.execute(sql, (mail_id, user_id))
      result = cursor.fetchone()

    if result:
      mail_id, thread_id, mailbox_id, user_id, category_label, is_processed = result
      return MailMetadata(
        mail_id=mail_id,
        thread_id=thread_id,
        mailbox_id=mailbox_id,
        user_id=user_id,
        category_label=category_label,
        is_processed=is_processed
      )
    return None
