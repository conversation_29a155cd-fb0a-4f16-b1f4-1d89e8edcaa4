import asyncio
import time
from typing import List, Optional
from psycopg2.extras import RealDictCursor
from fastapi import APIRouter, Depends, HTTPException, Request, Query
from fastapi.responses import StreamingResponse
import requests
import redis

# SuperTokens imports
from supertokens_python.recipe.session.framework.fastapi import verify_session
from supertokens_python.recipe.session import SessionContainer

from src.config import logger, settings
from src.config.pagination import DEFAULT_PAGE_SIZE, MAX_PAGE_SIZE, SEARCH_RESULTS_LIMIT
from src.db.connection import PostgresConnection, get_connection_pool
from src.pydantic_models import SendMailReplyRequest, SaveMailDraftRequest, SkipMailRequest, RegenerateDraftRequest, ApiResponse
from pydantic import BaseModel

class BatchMarkAsReadRequest(BaseModel):
    thread_ids: List[str]
from src.services.event_bus import event_bus
from src.constants import ORCH_STREAM

router = APIRouter()
# Counter for API calls to reduce logging noise
api_call_counters = {
  # Removed unused "/api/threads" counter
}

# Initialize the connection pool at startup
def init_db_pool():
  get_connection_pool(min_conn=2, max_conn=10)

# Call this at module load time to ensure the pool is created
init_db_pool()

def get_db_conn():
  """Get a database connection from the pool"""
  conn = PostgresConnection()
  conn.connect()
  return conn

async def get_current_user(session: SessionContainer = Depends(verify_session())):
  """Extract user_id from authenticated session"""
  return session.get_user_id()

@router.get("/health")
async def health():
    return {"status": "ok"}

@router.get("/threads/{thread_id}", response_model=dict)
async def get_thread_detail(
  thread_id: str,
  user_id: str = Depends(get_current_user),
  db_conn: PostgresConnection = Depends(get_db_conn)
):
  """
  Get detailed information about a thread, including the latest response.
  
  Implements graceful fallback: if thread not found in orchestrator.threads,
  checks mail_conn.mail_threads and returns basic thread data with processing status.
  """
  try:
    with db_conn.transaction(cursor_factory=RealDictCursor) as cur:
      # Get thread information from orchestrator (with user filtering)
      cur.execute("""
        SELECT
          thread_id,
          mailbox_id,
          status,
          processing_status,
          error_message,
          summary,
          created_at,
          updated_at
        FROM
          orchestrator.threads
        WHERE
          thread_id = %s AND user_id = %s
      """, (thread_id, user_id))

      thread_row = cur.fetchone()
      
      # If not found in orchestrator, check mail_conn and return basic data
      if not thread_row:
        logger.info(f"Thread {thread_id} not found in orchestrator.threads, checking mail_conn...")
        
        # Check if thread exists in mail_conn (with user filtering)
        cur.execute("""
          SELECT id, mailbox_id, last_updated
          FROM mail_conn.mail_threads
          WHERE id = %s AND user_id = %s
        """, (thread_id, user_id))
        
        mail_conn_thread = cur.fetchone()
        if not mail_conn_thread:
          raise HTTPException(status_code=404, detail="Thread not found in any database")
        
        # Return basic thread data with "not_processed" status
        logger.info(f"Thread {thread_id} exists in mail_conn but not yet processed by orchestrator")
        
        # Fetch mails from mail_conn with proper body parsing and attachments
        cur.execute("""
          SELECT
            m.id,
            m.from_email, 
            m.to_email,
            m.subject,
            m.body,
            m.timestamp,
            m.thread_position
          FROM mail_conn.mails m
          WHERE m.mail_thread_id = %s
          ORDER BY m.thread_position ASC
        """, (thread_id,))
        
        messages = []
        for row in cur.fetchall():
            message_data = dict(row)
            
            # Parse the JSONB body field to extract HTML content
            body_content = message_data.get('body', {})
            message_data['body'] = {
                'html': body_content.get('html', '<p>No content available</p>'),
                'attachments': []  # Will be populated below
            }
            
            # Fetch attachments for this message
            cur.execute("""
              SELECT filename, content_type, content, size, cid
              FROM mail_conn.mail_attachments
              WHERE mail_id = %s
            """, (message_data['id'],))
            
            attachments = []
            for att_row in cur.fetchall():
              att_data = dict(att_row)
              att_data['cid'] = att_row.get('cid')
              # Convert binary content to base64 if needed
              if isinstance(att_data['content'], (bytes, memoryview)):
                import base64
                att_data['content'] = base64.b64encode(
                  att_data['content'].tobytes() if isinstance(att_data['content'], memoryview) 
                  else att_data['content']
                ).decode('utf-8')
              attachments.append(att_data)
            
            message_data['body']['attachments'] = attachments
            messages.append(message_data)
        
        # Return thread data with clear processing status
        return {
          "thread_id": thread_id,
          "mailbox_id": mail_conn_thread['mailbox_id'],
          "status": "active",
          "processing_status": "not_processed",
          "error_message": None,
          "summary": None,
          "created_at": mail_conn_thread['last_updated'].isoformat(),
          "updated_at": mail_conn_thread['last_updated'].isoformat(),
          "messages": messages,
          "responses": [],
          "_source": "mail_conn_fallback"  # Indicator for frontend
        }

      # Normal path: thread exists in orchestrator
      thread_detail = dict(thread_row)
      
      # Fetch all mails for this thread with proper body parsing and attachments
      cur.execute("""
        SELECT
          m.id,
          m.from_email, 
          m.to_email,
          m.subject,
          m.body,
          m.timestamp,
          m.thread_position
        FROM mail_conn.mails m
        WHERE m.mail_thread_id = %s
        ORDER BY m.thread_position ASC
      """, (thread_id,))
      
      # Process the results with proper body parsing and attachment inclusion
      messages = []
      for row in cur.fetchall():
          message_data = dict(row)
          
          # Parse the JSONB body field to extract HTML content
          body_content = message_data.get('body', {})
          message_data['body'] = {
              'html': body_content.get('html', '<p>No content available</p>'),
              'attachments': []  # Will be populated below
          }
          
          # Fetch attachments for this message
          cur.execute("""
            SELECT filename, content_type, content, size, cid
            FROM mail_conn.mail_attachments
            WHERE mail_id = %s
          """, (message_data['id'],))
          
          attachments = []
          for att_row in cur.fetchall():
            att_data = dict(att_row)
            att_data['cid'] = att_row.get('cid')
            # Convert binary content to base64 if needed
            if isinstance(att_data['content'], (bytes, memoryview)):
              import base64
              att_data['content'] = base64.b64encode(
                att_data['content'].tobytes() if isinstance(att_data['content'], memoryview) 
                else att_data['content']
              ).decode('utf-8')
            attachments.append(att_data)
          
          message_data['body']['attachments'] = attachments
          messages.append(message_data)
          
      thread_detail["messages"] = messages
      thread_detail["responses"] = []

      # Get all responses for this thread
      cur.execute("""
        SELECT
          id,
          mail_id,
          category_label,
          agent_type,
          agent_data,
          status,
          created_at,
          updated_at
        FROM
          orchestrator.responses
        WHERE
          thread_id = %s
        ORDER BY
          created_at DESC
      """, (thread_id,))

      for row in cur.fetchall():
        response = dict(row)

        # Get QA pairs for this response
        cur.execute("""
          SELECT
            id,
            question,
            answer,
            metadata
          FROM
            orchestrator.qa_pairs
          WHERE
            response_id = %s
        """, (response["id"],))

        qa_pairs = [dict(row) for row in cur.fetchall()]
        response["qa_pairs"] = qa_pairs
        thread_detail["responses"].append(response)

      return thread_detail
  except Exception as e:
    logger.error(f"Error fetching thread detail: {e}")
    raise HTTPException(status_code=500, detail=str(e))
  finally:
    db_conn.close()


@router.get("/responses/{response_id}", response_model=dict)
async def get_response_detail(
  response_id: str,
  db_conn: PostgresConnection = Depends(get_db_conn)
):
  """
  Get detailed information about a response, including QA pairs.
  """
  try:
    with db_conn.transaction(cursor_factory=RealDictCursor) as cur:
      # Get response information
      cur.execute("""
        SELECT
          id,
          thread_id,
          mail_id,
          category_label,
          agent_type,
          agent_data,
          status,
          created_at,
          updated_at
        FROM
          orchestrator.responses
        WHERE
          id = %s
      """, (response_id,))

      response_row = cur.fetchone()
      if not response_row:
        raise HTTPException(status_code=404, detail="Response not found")

      response_detail = dict(response_row)

      # Get QA pairs for this response
      cur.execute("""
        SELECT
          id,
          question,
          answer,
          metadata
        FROM
          orchestrator.qa_pairs
        WHERE
          response_id = %s
      """, (response_id,))

      qa_pairs = [dict(row) for row in cur.fetchall()]
      response_detail["qa_pairs"] = qa_pairs

      return response_detail
  except Exception as e:
    logger.error(f"Error fetching response detail: {e}")
    raise HTTPException(status_code=500, detail=str(e))
  finally:
    db_conn.close()

@router.get("/events")
async def sse_endpoint(request: Request):
  """
  Streams EventBus messages to the browser.

  • Sends a comment every 15 s so Docker Desktop / Nginx / ALB
    never mark the socket idle.
  • Adds 'retry: 3000' so browsers back-off 3 s before reconnecting
    if the server drops during a deploy.
  """
  client_id = id(request)
  opened_at = time.time()
  client_ip = request.client.host if request.client else "unknown"
  logger.info(f"SSE connect id={client_id} ip={client_ip}")

  sub_id = event_bus.subscribe('*')            # ← one queue for this tab

  async def event_stream():
    KEEPALIVE = 15            # seconds
    last_ping = time.time()

    yield "retry: 3000\n\n"   # EventSource reconnect delay

    try:
      while True:
        # tab closed?
        if await request.is_disconnected():
          break

        # wait up to 1 s for a message
        msg = await event_bus.get_message(sub_id, timeout=1.0)
        if msg:
          yield f"data: {msg}\n\n"

        # keep-alive comment
        now = time.time()
        if now - last_ping >= KEEPALIVE:
          yield ": keep-alive\n\n"
          last_ping = now

    except asyncio.CancelledError:
      logger.info(f"SSE id={client_id} cancelled after {time.time()-opened_at:.1f}s")
    finally:
      event_bus.unsubscribe(sub_id)
      logger.info(f"SSE id={client_id} closed after {time.time()-opened_at:.1f}s")

  return StreamingResponse(
    event_stream(),
    media_type="text/event-stream",
    headers={
      "Cache-Control":               "no-cache, no-transform",
      "Connection":                  "keep-alive",
      "X-Accel-Buffering":           "no",
      "Access-Control-Allow-Origin": "*",
    },
  )

# @router.get("/responses-view", response_model=List[dict])
# async def get_responses_view(
#   limit: int = 20,
#   offset: int = 0,
#   after: Optional[str] = Query(None, description="RFC3339 timestamp to get responses updated after a certain time"),
#   status: Optional[str] = None,
#   db_conn: PostgresConnection = Depends(get_db_conn)
# ):
#   """
#   DISABLED: This endpoint referenced non-existent v_responses_overview view
#   Frontend uses /threads-unified instead
#   """
#   pass

@router.post("/mail/send-reply", response_model=ApiResponse)
async def send_mail_reply(
  request: SendMailReplyRequest,
  db_conn: PostgresConnection = Depends(get_db_conn)
):
  """
  Send a reply to an email via the mail_connection service.
  
  Parameters:
  - request: SendMailReplyRequest containing thread_id, mail_id, reply_text, and recipients
  """
  try:
    # Forward the request to the mail_connection service
    mail_conn_url = f"{settings.MAIL_CONNECTION_URL}/send-reply"
    
    # Transform the request to match mail_connection's MailReplyRequest
    mail_conn_request = {
      "mail_id": request.mail_id,
      "reply_text": request.reply_text,
      "recipients": request.recipients,
    }
    
    # Call the mail_connection service
    response = requests.post(mail_conn_url, json=mail_conn_request)
    
    # Check if the request was successful
    if response.status_code != 200:
      logger.error(f"Error sending reply: {response.text}")
      return ApiResponse(
        status="error", 
        message=f"Failed to send reply: {response.text}"
      )
    
    # Parse the response
    mail_conn_response = response.json()
    
    # Update the database to reflect that the reply was sent
    with db_conn.transaction() as cur:
      # Update the thread status
      cur.execute("""
        UPDATE orchestrator.threads
        SET processing_status = 'reply_sent',
            updated_at = NOW()
        WHERE thread_id = %s
      """, (request.thread_id,))
    
    return ApiResponse(
      status="success",
      message="Reply sent successfully",
      data=mail_conn_response
    )
    
  except Exception as e:
    logger.error(f"Error sending reply: {e}")
    raise HTTPException(status_code=500, detail=str(e))
  finally:
    db_conn.close()

@router.post("/mail/save-draft", response_model=ApiResponse)
async def save_mail_draft(
  request: SaveMailDraftRequest,
  db_conn: PostgresConnection = Depends(get_db_conn)
):
  """
  Save a draft reply to an email via the mail_connection service.
  
  Parameters:
  - request: SaveMailDraftRequest containing thread_id, mail_id, and draft_text
  """
  try:
    # Forward the request to the mail_connection service
    mail_conn_url = f"{settings.MAIL_CONNECTION_URL}/create_draft_reply/"
    
    # Transform the request to match mail_connection's MailReplyRequest
    mail_conn_request = {
      "mail_id": request.mail_id,
      "reply_text": request.draft_text
    }
    
    # Call the mail_connection service
    response = requests.post(mail_conn_url, json=mail_conn_request)
    
    # Check if the request was successful
    if response.status_code != 200:
      logger.error(f"Error saving draft: {response.text}")
      return ApiResponse(
        status="error", 
        message=f"Failed to save draft: {response.text}"
      )
    
    # Parse the response
    mail_conn_response = response.json()
    
    # Update the database to reflect that the draft was saved
    with db_conn.transaction() as cur:
      # Update the thread status
      cur.execute("""
        UPDATE orchestrator.threads
        SET processing_status = 'draft_saved',
            updated_at = NOW()
        WHERE thread_id = %s
      """, (request.thread_id,))
    
    return ApiResponse(
      status="success",
      message="Draft saved successfully",
      data=mail_conn_response
    )
    
  except Exception as e:
    logger.error(f"Error saving draft: {e}")
    raise HTTPException(status_code=500, detail=str(e))
  finally:
    db_conn.close()

@router.post("/mail/skip", response_model=ApiResponse)
async def skip_mail(
  request: SkipMailRequest,
  db_conn: PostgresConnection = Depends(get_db_conn)
):
  """
  Mark a thread as skipped.
  
  Parameters:
  - request: SkipMailRequest containing thread_id and optional reason
  """
  try:
    # Update the database to mark the thread as skipped
    with db_conn.transaction() as cur:
      cur.execute("""
        UPDATE orchestrator.threads
        SET processing_status = 'skipped',
            updated_at = NOW()
        WHERE thread_id = %s
      """, (request.thread_id,))
      
      # If a reason was provided, save it in the database
      if request.reason:
        cur.execute("""
          UPDATE orchestrator.threads
          SET error_message = %s
          WHERE thread_id = %s
        """, (request.reason, request.thread_id))
    
    return ApiResponse(
      status="success",
      message="Thread marked as skipped",
      data={"thread_id": request.thread_id}
    )
    
  except Exception as e:
    logger.error(f"Error skipping thread: {e}")
    raise HTTPException(status_code=500, detail=str(e))
  finally:
    db_conn.close()

@router.get("/search/mails", response_model=List[dict])
async def search_mails(
  q: str = Query(..., description="Search query string"),
  limit: int = Query(SEARCH_RESULTS_LIMIT, description="Maximum number of results to return"),
  db_conn: PostgresConnection = Depends(get_db_conn)
):
  """
  Search mails using full-text search across subject, content, from_address, AI responses, and summaries.
  Optimized for fast command palette search.
  
  Parameters:
  - q: Search query string (minimum 2 characters)
  - limit: Maximum number of results to return (default 8)
  """
  try:
    if len(q.strip()) < 2:
      return []
    
    results = db_conn.search_mails(q, limit)
    return results
    
  except Exception as e:
    logger.error(f"Error searching mails: {e}")
    raise HTTPException(status_code=500, detail=str(e))
  finally:
    db_conn.close()

@router.post("/mail/regenerate-draft", response_model=ApiResponse)
async def regenerate_draft(
  request: RegenerateDraftRequest,
  db_conn: PostgresConnection = Depends(get_db_conn)
):
  """
  Regenerate a draft reply for an email thread.
  This will trigger a new agent run and update the existing response.
  
  Parameters:
  - request: RegenerateDraftRequest with thread_id and response_id
  """
  try:
    # Get the thread_id and response_id
    thread_id = request.thread_id
    response_id = request.response_id
    
    logger.info(f"Regenerating draft for thread {thread_id}, response {response_id}")
    
    # Call the agent service to regenerate the draft
    # This is done asynchronously - we'll just queue it and return success
    
    # First, update the status to indicate regeneration is in progress
    with db_conn.transaction() as cur:
      cur.execute("""
        UPDATE orchestrator.responses
        SET status = 'regenerating'
        WHERE id = %s AND thread_id = %s
      """, (response_id, thread_id))
      
      if cur.rowcount == 0:
        raise HTTPException(status_code=404, detail="Response not found")
    
    # Publish the regenerate event to the orchestrator_events stream
    # The event will be picked up by the fanout service and broadcasted
    # to the frontend via the EventBus
    redis_client = redis.Redis(
      host=settings.REDIS_HOST, 
      port=settings.REDIS_PORT, 
      decode_responses=True
    )
    
    redis_client.xadd(
      ORCH_STREAM,
      {
        "event": "regenerate_draft",
        "thread_id": thread_id,
        "response_id": response_id,
        "ts": int(time.time())
      },
      maxlen=10000,
      approximate=True
    )
    
    # TODO: Trigger the actual regeneration process
    # This would typically be done by a background task or worker
    
    return ApiResponse(
      status="success",
      message="Draft regeneration has been queued",
      data={"thread_id": thread_id, "response_id": response_id}
    )
    
  except HTTPException:
    # Re-raise HTTP exceptions
    raise
  except Exception as e:
    logger.error(f"Error regenerating draft: {e}")
    raise HTTPException(status_code=500, detail=str(e))

@router.get("/threads-unified", response_model=List[dict])
async def get_threads_unified(
  limit: int = DEFAULT_PAGE_SIZE,
  offset: int = 0,
  after: Optional[str] = Query(None, description="RFC3339 timestamp to get threads updated after a certain time"),
  read_status: Optional[str] = Query(None, description="Filter by read status: 'read', 'unread', or 'all'"),
  response_status: Optional[str] = Query(None, description="Filter by response status: 'pending', 'completed', or 'all'"),
  user_id: str = Depends(get_current_user),
  db_conn: PostgresConnection = Depends(get_db_conn)
):
  """
  Get a unified list of all threads with optional AI enrichment data.
  This replaces both /threads and /responses-view endpoints with a single, clean interface.

  Parameters:
  - limit: Maximum number of threads to return (default: 50, max: 500)
  - offset: Number of threads to skip  
  - after: RFC3339 timestamp to get threads updated after a certain time
  - read_status: Filter by read status ('read', 'unread', 'all')
  - response_status: Filter by response status ('pending', 'completed', 'all')
  """
  # Enforce maximum limit to prevent performance issues
  if limit > MAX_PAGE_SIZE:
    limit = MAX_PAGE_SIZE
    
  try:
    with db_conn.transaction(cursor_factory=RealDictCursor) as cur:
      # Build the query with filters (including user_id)
      sql = """
      SELECT
        thread_id,
        mailbox_id,
        thread_status,
        processing_status,
        error_message,
        summary,
        is_read,
        thread_created_at,
        thread_updated_at,
        subject,
        from_address,
        to_addresses,
        body_html,
        mail_timestamp,
        response_id,
        response_status,
        agent_type,
        response_created_at,
        response_updated_at,
        category_label,
        ai_response,
        ai_summary,
        topic_labels,
        last_updated
      FROM
        orchestrator.v_threads_unified
      WHERE user_id = %s
      """
      params = [user_id]
      
      # Add filters
      if after:
        sql += " AND last_updated > %s"
        params.append(after)
        
      if read_status and read_status != 'all':
        if read_status == 'read':
          sql += " AND is_read = true"
        elif read_status == 'unread':
          sql += " AND is_read = false"
          
      if response_status and response_status != 'all':
        if response_status == 'pending':
          sql += " AND (response_status = 'pending' OR response_status = 'pending_review' OR response_status IS NULL)"
        elif response_status == 'completed':
          sql += " AND (response_status = 'completed' OR response_status = 'sent')"
      
      sql += " ORDER BY mail_timestamp DESC LIMIT %s OFFSET %s"
      params.extend([str(limit), str(offset)])
      
      cur.execute(sql, tuple(params))

      threads = []
      for row in cur.fetchall():
        threads.append(dict(row))

      return threads
  except Exception as e:
    logger.error(f"Error fetching unified threads: {e}")
    raise HTTPException(status_code=500, detail=str(e))
  finally:
    db_conn.close()

@router.get("/debug/thread-counts", response_model=dict)
async def get_thread_counts(
  db_conn: PostgresConnection = Depends(get_db_conn)
):
  """
  Debug endpoint to get thread counts by various statuses.
  Helps understand what data exists in the database.
  """
  try:
    with db_conn.transaction(cursor_factory=RealDictCursor) as cur:
      # Get total thread count
      cur.execute("SELECT COUNT(*) as total FROM orchestrator.threads")
      total_threads = cur.fetchone()['total']
      
      # Get thread counts by processing status
      cur.execute("""
        SELECT processing_status, COUNT(*) as count 
        FROM orchestrator.threads 
        GROUP BY processing_status
      """)
      thread_status_counts = {row['processing_status']: row['count'] for row in cur.fetchall()}
      
      # Get response counts by status
      cur.execute("""
        SELECT status, COUNT(*) as count 
        FROM orchestrator.responses 
        GROUP BY status
      """)
      response_status_counts = {row['status']: row['count'] for row in cur.fetchall()}
      
      # Get unified view counts
      cur.execute("""
        SELECT 
          COUNT(*) as total,
          COUNT(response_id) as with_responses,
          COUNT(*) - COUNT(response_id) as without_responses
        FROM orchestrator.v_threads_unified
      """)
      unified_counts = dict(cur.fetchone())
      
      # Get counts by response status in unified view
      cur.execute("""
        SELECT 
          COALESCE(response_status, 'no_response') as status,
          COUNT(*) as count
        FROM orchestrator.v_threads_unified
        GROUP BY response_status
      """)
      unified_response_counts = {row['status']: row['count'] for row in cur.fetchall()}

      return {
        "total_threads": total_threads,
        "thread_processing_status": thread_status_counts,
        "response_status": response_status_counts,
        "unified_view": unified_counts,
        "unified_response_status": unified_response_counts
      }
  except Exception as e:
    logger.error(f"Error fetching thread counts: {e}")
    raise HTTPException(status_code=500, detail=str(e))
  finally:
    db_conn.close()

@router.post("/threads/{thread_id}/mark-read", response_model=ApiResponse)
async def mark_thread_as_read(
  thread_id: str,
  db_conn: PostgresConnection = Depends(get_db_conn)
):
  """
  Mark a thread as read in the database with optimized conditional updates.
  
  Returns:
  - changed: boolean indicating if the read status was actually modified
  - is_read: current read status
  - thread_id: the thread ID
  """
  try:
    with db_conn.transaction(cursor_factory=RealDictCursor) as cur:
      # ✅ Step 1: Check current read status first
      cur.execute("""
        SELECT is_read 
        FROM orchestrator.threads 
        WHERE thread_id = %s
      """, (thread_id,))
      
      result = cur.fetchone()
      
      # Handle case where thread doesn't exist yet
      if not result:
        logger.info(f"Thread {thread_id} not found in orchestrator - ensuring it exists")
        # Create thread if it doesn't exist (existing logic)
        db_conn.ensure_thread_exists(thread_id, None)
        
        # Re-check after creation
        cur.execute("""
          SELECT is_read 
          FROM orchestrator.threads 
          WHERE thread_id = %s
        """, (thread_id,))
        result = cur.fetchone()
      
      current_read_status = result['is_read'] if result else False
      
      # ✅ Step 2: Early return if already read
      if current_read_status:
        logger.debug(f"Thread {thread_id} already marked as read - no database update needed")
        return ApiResponse(
          status="success",
          message="Thread already marked as read",
          data={
            "thread_id": thread_id,
            "is_read": True,
            "changed": False,
            "optimization": "conditional_update"
          }
        )
      
      # ✅ Step 3: Update only if currently unread
      cur.execute("""
        UPDATE orchestrator.threads
        SET is_read = TRUE,
            updated_at = NOW()
        WHERE thread_id = %s 
          AND is_read = FALSE
      """, (thread_id,))
      
      rows_affected = cur.rowcount
      
      if rows_affected == 0:
        # Race condition: another request marked it as read
        logger.debug(f"Thread {thread_id} was marked as read by another request")
        return ApiResponse(
          status="success",
          message="Thread was already marked as read by another request",
          data={
            "thread_id": thread_id,
            "is_read": True,
            "changed": False,
            "optimization": "race_condition_detected"
          }
        )
      
      logger.info(f"Thread {thread_id} successfully marked as read")
      return ApiResponse(
        status="success",
        message="Thread marked as read",
        data={
          "thread_id": thread_id,
          "is_read": True,
          "changed": True,
          "optimization": "database_updated"
        }
      )
      
  except HTTPException:
    # Re-raise HTTP exceptions
    raise
  except Exception as e:
    logger.error(f"Error marking thread {thread_id} as read: {e}")
    raise HTTPException(status_code=500, detail=str(e))
  finally:
    db_conn.close()


@router.post("/threads/mark-read-batch", response_model=ApiResponse)
async def mark_threads_as_read_batch(
    request: BatchMarkAsReadRequest,
    db_conn: PostgresConnection = Depends(get_db_conn)
):
    """
    Mark multiple threads as read in a single database transaction.
    
    Optimized for bulk operations with minimal database roundtrips.
    """
    try:
        thread_ids = request.thread_ids
        if not thread_ids:
            return ApiResponse(
                status="success",
                message="No threads to process",
                data={"successful_threads": [], "failed_threads": []}
            )
        
        if len(thread_ids) > 50:  # Safety limit
            raise HTTPException(
                status_code=400, 
                detail="Too many threads in batch (max 50)"
            )
        
        successful_threads = []
        failed_threads = []
        
        with db_conn.transaction(cursor_factory=RealDictCursor) as cur:
            # ✅ Batch check current read status
            placeholders = ','.join(['%s'] * len(thread_ids))
            cur.execute(f"""
                SELECT thread_id, is_read 
                FROM orchestrator.threads 
                WHERE thread_id IN ({placeholders})
            """, thread_ids)
            
            existing_threads = {row['thread_id']: row['is_read'] for row in cur.fetchall()}
            
            # Identify threads that need updating
            threads_to_update = []
            for thread_id in thread_ids:
                if thread_id in existing_threads:
                    if not existing_threads[thread_id]:  # Currently unread
                        threads_to_update.append(thread_id)
                    successful_threads.append(thread_id)  # Already read or will be updated
                else:
                    # Thread doesn't exist - ensure it exists first
                    try:
                        db_conn.ensure_thread_exists(thread_id, None)
                        threads_to_update.append(thread_id)
                        successful_threads.append(thread_id)
                    except Exception as e:
                        logger.error(f"Failed to ensure thread {thread_id} exists: {e}")
                        failed_threads.append(thread_id)
            
            # ✅ Batch update threads that need it
            if threads_to_update:
                update_placeholders = ','.join(['%s'] * len(threads_to_update))
                cur.execute(f"""
                    UPDATE orchestrator.threads
                    SET is_read = TRUE,
                        updated_at = NOW()
                    WHERE thread_id IN ({update_placeholders})
                      AND is_read = FALSE
                """, threads_to_update)
                
                logger.info(f"Batch updated {cur.rowcount} threads as read")
        
        return ApiResponse(
            status="success",
            message=f"Processed {len(successful_threads)} threads successfully",
            data={
                "successful_threads": successful_threads,
                "failed_threads": failed_threads,
                "updated_count": len(threads_to_update),
                "already_read_count": len(successful_threads) - len(threads_to_update)
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in batch mark-as-read: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        db_conn.close()