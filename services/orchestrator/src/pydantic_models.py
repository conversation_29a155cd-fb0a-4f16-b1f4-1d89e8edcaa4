from pydantic import BaseModel, model_validator, Field
from typing import List, Literal, get_args, Optional, Union, Dict, Any
from datetime import datetime

ImageAttachment = Literal["image/png", "image/jpeg", "image/gif", "image/webp", "image/heic"]
VideoAttachment = Literal["video/mp4", "video/quicktime", "video/x-msvideo", "video/x-ms-wmv"]
AudioAttachment = Literal["audio/mpeg", "audio/mp3", "audio/mpga", "audio/m4a", "audio/ogg", "audio/wav", "audio/webm"]
DocumentAttachment = Literal["application/pdf", "text/plain"]
ALLOWED_ATTACHMENT_TYPES = set(get_args(ImageAttachment) + get_args(VideoAttachment) + get_args(AudioAttachment) + get_args(DocumentAttachment))


class MailAttachment(BaseModel):
  filename: str
  content_type: Literal[ImageAttachment, VideoAttachment, AudioAttachment, DocumentAttachment]
  content: bytes
  size: int


class MailBody(BaseModel):
  html: str  # HTML content - the single source of truth
  attachments: List[MailAttachment] = []


class Mail(BaseModel):
  id: str
  mail_thread_id: str
  mailbox_id: str
  subject: str
  body: MailBody
  from_email: str
  to_email: str
  received_or_sent: Literal["received", "sent"]
  timestamp: datetime
  thread_position: int
  user_id: str
  additional_metadata: dict = {}


class MailThread(BaseModel):
  id: str
  mailbox_id: str
  user_id: str
  mails: List[Mail]

class Category(BaseModel):
    id: Optional[int] = None
    label: str
    description: Optional[str] = None
    created_at: Optional[datetime] = None

class Thread(BaseModel):
    thread_id: str
    mailbox_id: str
    user_id: str
    status: str = "active"
    processing_status: str = "pending"  # pending, processing, succeeded, failed
    error_message: Optional[str] = None
    summary: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class MailMetadata(BaseModel):
    mail_id: str
    thread_id: str
    mailbox_id: str
    user_id: str
    category_label: Optional[str] = None
    is_processed: bool = False
    updated_at: Optional[datetime] = None


class QAPair(BaseModel):
  question: str
  answer: str
  response_id: Optional[str] = None
  thread_id: Optional[str] = None
  metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)

class TopicLabel(BaseModel):
  label: str
  confidence: float

class SearchResult(QAPair):
  """Represents a search result, extending QAPair with a relevance score

  Args:
    score: The relevance score of the search result
  """

  score: float
  underlying_scores: Optional[List[float]] = None

class QAAgentResponse(BaseModel):
  """Response from the agent service's /agent/mail endpoint"""
  reply: str
  qa_pairs: List[QAPair]
  topic_labels: List[TopicLabel]
  summary: str
  sources_contained_answer: bool = False
  sources: Optional[List[SearchResult]] = None

  @model_validator(mode="before")
  def ensure_lists(cls, values):
    """Ensure all list fields have at least empty lists"""
    if "qa_pairs" not in values or values["qa_pairs"] is None:
      values["qa_pairs"] = []
    if "topic_labels" not in values or values["topic_labels"] is None:
      values["topic_labels"] = []
    return values


class AgentResponse(BaseModel):
    """
    Generalized agent response model that can store different types of agent responses.
    The agent_type field indicates what type of response is stored.
    The actual agent response is stored in agent_data, which can be one of several types.
    """
    id: Optional[str] = None  # UUID
    thread_id: str
    mailbox_id: str
    user_id: str
    mail_id: Optional[str] = None
    category_label: Optional[str] = None
    # Type of agent response - 'qa_agent', 'summary_agent', etc.
    agent_type: Literal["qa_agent",]
    # Store the complete agent response data
    agent_data: Union[QAAgentResponse, None] = Field(default=None)
    # Status for review workflow
    status: str = "pending_review"
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    @classmethod
    def from_qa_agent_response(
        cls,
        qa_response: QAAgentResponse,
        thread_id: str,
        mailbox_id: str,
        user_id: str,
        mail_id: Optional[str] = None,
        category_label: Optional[str] = None,
    ) -> "AgentResponse":
        """
        Factory method to create an AgentResponse from a QAAgentResponse
        """
        return cls(
            thread_id=thread_id,
            mailbox_id=mailbox_id,
            user_id=user_id,
            mail_id=mail_id,
            category_label=category_label,
            agent_type="qa_agent",
            agent_data=qa_response.model_dump(mode='json'),
            status="pending_review"
        )

    def get_qa_response(self) -> Optional[QAAgentResponse]:
        """
        Extract QAAgentResponse from this response if it's a QA agent response
        """
        if self.agent_type == "qa_agent" and self.agent_data:
            return QAAgentResponse(**self.agent_data)
        return None

class SendMailReplyRequest(BaseModel):
  thread_id: str
  mail_id: str
  reply_text: str
  recipients: Optional[str] = None

class ApiResponse(BaseModel):
  status: str
  message: str
  data: Optional[Dict[str, Any]] = None

class SaveMailDraftRequest(BaseModel):
  thread_id: str
  mail_id: str
  draft_text: str

class SkipMailRequest(BaseModel):
  thread_id: str
  reason: Optional[str] = None

class RegenerateDraftRequest(BaseModel):
  thread_id: str
  mail_id: str
  response_id: str
