import time
from typing import Callable
import asyncio
import redis.asyncio as aioredis
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware

# SuperTokens imports
from supertokens_python import init, InputAppInfo, SupertokensConfig
from supertokens_python.recipe import session
from supertokens_python.framework.fastapi import get_middleware

# Counter for health check requests
health_check_counter = {
  "count": 0,
  "last_logged": time.time()
}

from src.config import logger
from src.controllers.api_controller import router as api_router, init_db_pool
from src.controllers.chat_bridge import router as chat_router
from src.services.redis_service import close_redis_pool, redis_pool
from src.services.stream_fanout import fanout_loop

# Initialize SuperTokens
init(
    app_info=InputAppInfo(
        app_name="ClariQ Orchestrator",
        api_domain="http://localhost:8003",
        website_domain="http://localhost:7777",
        api_base_path="/api/v1/auth",
    ),
    supertokens_config=SupertokensConfig(
        connection_uri="http://localhost:3567"
    ),
    framework="fastapi",
    recipe_list=[session.init()]
)

@asynccontextmanager
async def lifespan(app: FastAPI):
  """Lifecycle manager for the FastAPI app."""
  logger.info("Initializing DB pool...")
  init_db_pool()
  logger.info("DB pool ready")
  
  # Test Redis connection before starting
  try:
    redis = aioredis.Redis(connection_pool=redis_pool)
    await redis.ping()
    logger.info("Redis connection test successful")
  except Exception as e:
    logger.error(f"Redis connection test failed: {e}")
    # Continue anyway, as Redis might come online later
  
  # Start the stream fanout task
  logger.info("Starting stream fanout task...")
  fanout_task = asyncio.create_task(fanout_loop())
  logger.info("Stream fanout task started")
  
  yield # shut down things after yield
  
  # Cancel the fanout task
  if fanout_task:
    logger.info("Cancelling stream fanout task...")
    fanout_task.cancel()
    try:
      await fanout_task
    except asyncio.CancelledError:
      pass
  
  # Close Redis pool
  await close_redis_pool()
  logger.info("Shutting down...")

def create_app() -> FastAPI:
  """Create and configure the FastAPI application."""
  app = FastAPI(
    title="ClariQ Orchestrator API",
    version="0.1.0",
    description="API for the ClariQ Orchestrator service",
    lifespan=lifespan
  )

  # Add SuperTokens middleware first
  app.add_middleware(get_middleware())

  # Add CORS middleware
  app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
  )

  # Skip Gzip compression for streaming responses
  # app.add_middleware(GZipMiddleware, minimum_size=1000)

  # Add request logging middleware
  @app.middleware("http")
  async def log_requests(request: Request, call_next: Callable):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time

    # Log all requests except health and threads (which have their own counters)
    if request.url.path != "/health" and request.url.path != "/api/threads":
      logger.info(
        f"Request: {request.method} {request.url.path} - "
        f"Status: {response.status_code} - "
        f"Process time: {process_time:.4f}s"
      )
    return response

  # Include API router
  app.include_router(api_router, prefix="/api")
  app.include_router(chat_router, prefix="/api")

  @app.get("/health")
  async def health_check():
    """Health check endpoint."""
    # Update counter and log periodically
    health_check_counter["count"] += 1
    current_time = time.time()

    # Log a summary every 60 seconds
    if current_time - health_check_counter["last_logged"] > 60:
      logger.info(f"API call summary: /health called {health_check_counter['count']} times in the last 60 seconds")
      health_check_counter["count"] = 0
      health_check_counter["last_logged"] = current_time

    return {"status": "healthy"}
    
  @app.get("/redis-stats")
  async def redis_stats():
    """Get Redis connection statistics."""
    try:
      redis = aioredis.Redis(connection_pool=redis_pool)
      info = await redis.info()
      clients_info = await redis.info('clients')
      
      conn_stats = {
        "pool": {
          "max_connections": redis_pool.max_connections,
          "connection_class": str(redis_pool.connection_class),
          "pid": redis_pool.pid
        },
        "redis": {
          "connected_clients": clients_info.get('connected_clients', 'N/A'),
          "blocked_clients": clients_info.get('blocked_clients', 'N/A'),
          "uptime_in_seconds": info.get('uptime_in_seconds', 'N/A'),
          "used_memory_human": info.get('used_memory_human', 'N/A')
        }
      }
      return conn_stats
    except Exception as e:
      logger.error(f"Error getting Redis stats: {e}")
      return {"error": str(e)}

  return app

app = create_app()

if __name__ == "__main__":
  import uvicorn
  uvicorn.run(
    app, 
    host="0.0.0.0", 
    port=8000,
    # Container-optimized settings for streaming
    loop="asyncio",
    http="h11",  # Use h11 for better streaming
    ws="none",   # Disable websockets if not needed
    lifespan="on",
    access_log=False,  # Reduce logging overhead
    # Critical for streaming in containers
    timeout_keep_alive=65,
    timeout_graceful_shutdown=30,
  )
