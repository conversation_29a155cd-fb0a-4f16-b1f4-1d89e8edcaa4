import json
from base64 import b64encode
import psycopg2
from psycopg2.extensions import connection as psycopg2_connection
from psycopg2.extras import RealDict<PERSON>urs<PERSON>
from contextlib import contextmanager
from datetime import datetime
from typing import List, Optional, Generator

from src.config import logger, settings
from src.producer import notify_new_mail, notify_new_thread
from src.pydantic_models import Mail, MailThread, MailBox, MailAttachment, MailBody
from src.db.statements import (
    SQL_CREATE_USER_GOOGLE_TOKENS_TABLE,
    SQL_CREATE_MAILBOXES_TABLE, 
    SQL_CREATE_MAIL_THREADS_TABLE, 
    SQL_CREATE_MAILS_TABLE, 
    SQL_CREATE_ATTACHMENTS_TABLE, 
    SQL_CREATE_INDEXES
)

class PostgresConnection:
  """
  Manages PostgreSQL interactions: connect, create schema, store data, etc.
  All data access methods are user-scoped.
  """

  def __init__(self, host=settings.PG_HOST, port=settings.PG_PORT, user=settings.PG_USER, password=settings.PG_PASSWORD, dbname=settings.PG_DBNAME):
    self.conn_params = dict(host=host, port=port, user=user, password=password, dbname=dbname)
    self.conn: Optional[psycopg2_connection] = None

  def connect(self):
    """Establish connection and initialize schema if needed."""
    if self.conn:
      return
    self.conn = psycopg2.connect(**self.conn_params)
    self.conn.autocommit = False
    self._init_schema()
    logger.info(f"Connected to PostgreSQL: {self.conn_params['dbname']}")

  def close(self):
    """Close the DB connection gracefully."""
    if self.conn:
      self.conn.close()
      self.conn = None
      logger.info("Closed DB connection.")

  @contextmanager
  def transaction(self, cursor_factory=None) -> Generator:
    """
    Transaction context manager. Any exception rolls back; otherwise commits.
    """
    if not self.conn:
      self.connect()
    cursor = self.conn.cursor(cursor_factory=cursor_factory)
    try:
      yield cursor
      self.conn.commit()
    except Exception as e:
      self.conn.rollback()
      logger.error(f"Transaction failed: {e}")
      raise
    finally:
      cursor.close()

  def _init_schema(self):
    """Create necessary tables and indexes if they don't exist."""
    with self.transaction() as cur:
      cur.execute("CREATE SCHEMA IF NOT EXISTS mail_conn;")
      cur.execute("SET search_path TO mail_conn, public;")
      queries = [
          SQL_CREATE_USER_GOOGLE_TOKENS_TABLE,
          SQL_CREATE_MAILBOXES_TABLE, 
          SQL_CREATE_MAIL_THREADS_TABLE, 
          SQL_CREATE_MAILS_TABLE, 
          SQL_CREATE_ATTACHMENTS_TABLE, 
          *SQL_CREATE_INDEXES
      ]
      for q in queries:
        cur.execute(q)
    logger.info("Schema initialized or verified.")

  def store_google_token(self, user_id: str, encrypted_refresh_token: bytes, token_source: str):
      """Store or update a user's encrypted Google refresh token."""
      with self.transaction() as cur:
          cur.execute(
              """INSERT INTO mail_conn.user_google_tokens (user_id, encrypted_refresh_token, token_source, updated_at)
                 VALUES (%s, %s, %s, NOW())
                 ON CONFLICT (user_id) DO UPDATE SET
                   encrypted_refresh_token = EXCLUDED.encrypted_refresh_token,
                   token_source = EXCLUDED.token_source,
                   updated_at = NOW();""",
              (user_id, encrypted_refresh_token, token_source)
          )
      logger.info(f"Stored Google token for user {user_id}")

  def get_google_token(self, user_id: str) -> Optional[bytes]:
      """Retrieve a user's encrypted Google refresh token."""
      with self.transaction() as cur:
          cur.execute("SELECT encrypted_refresh_token FROM mail_conn.user_google_tokens WHERE user_id = %s", (user_id,))
          row = cur.fetchone()
      return row[0] if row else None

  def store_mailbox(self, mailbox: MailBox, user_id: str):
    """
    Store a MailBox object and its threads/mails/attachments for a specific user.
    """
    with self.transaction() as cur:
      cur.execute("SELECT name FROM mail_conn.mailboxes WHERE id=%s AND user_id=%s", (mailbox.id, user_id))
      existing = cur.fetchone()
      if existing:
        if existing[0] != mailbox.name:
          cur.execute("UPDATE mail_conn.mailboxes SET name=%s, updated_at=NOW() WHERE id=%s AND user_id=%s", (mailbox.name, mailbox.id, user_id))
      else:
        cur.execute("INSERT INTO mail_conn.mailboxes (id, user_id, name) VALUES (%s, %s, %s)", (mailbox.id, user_id, mailbox.name))

      existing_threads = self._fetchall(cur, "SELECT id FROM mail_conn.mail_threads WHERE mailbox_id=%s AND user_id=%s", (mailbox.id, user_id))
      existing_thread_ids = {t[0] for t in existing_threads}

      for thread in mailbox.threads:
        if thread.id not in existing_thread_ids:
          self.store_thread(thread, user_id, mailbox.id)

  def store_thread(self, thread: MailThread, user_id: str, mailbox_id: Optional[str] = None):
    """
    Store a thread and its mails for a specific user.
    """
    with self.transaction() as cur:
      cur.execute("SELECT id FROM mail_conn.mail_threads WHERE id=%s AND user_id=%s", (thread.id, user_id))
      if not cur.fetchone():
        if not mailbox_id:
          raise ValueError("Need a mailbox_id for a new thread.")
        cur.execute("INSERT INTO mail_conn.mail_threads (id, user_id, mailbox_id, last_updated) VALUES (%s, %s, %s, NOW())", (thread.id, user_id, mailbox_id))
        notify_new_thread(thread.id, mailbox_id, user_id)
      else:
        if mailbox_id:
          cur.execute("UPDATE mail_conn.mail_threads SET mailbox_id=%s, last_updated=NOW() WHERE id=%s AND user_id=%s", (mailbox_id, thread.id, user_id))

      existing_mails = self._fetchall(cur, "SELECT id FROM mails WHERE mail_thread_id=%s AND user_id=%s", (thread.id, user_id))
      existing_mail_ids = {m[0] for m in existing_mails}

      for mail in thread.mails:
        if mail.id not in existing_mail_ids:
          self.store_mail(mail, user_id)

  def store_mail(self, mail: Mail, user_id: str):
    """Insert mail and attachments for a user if it doesn't already exist."""
    with self.transaction() as cur:
      cur.execute("SELECT id FROM mail_conn.mails WHERE id=%s AND user_id=%s", (mail.id, user_id))
      if cur.fetchone():
        return

      body_json = json.dumps({"html": mail.body.html})
      meta_json = json.dumps(mail.additional_metadata)
      cur.execute(
        """INSERT INTO mail_conn.mails
           (id, user_id, mail_thread_id, mailbox_id, subject, body, from_email, to_email, received_or_sent,
            timestamp, thread_position, additional_metadata)
           VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)""",
        (mail.id, user_id, mail.mail_thread_id, mail.mailbox_id, mail.subject, body_json, mail.from_email, mail.to_email, mail.received_or_sent, mail.timestamp, mail.thread_position, meta_json),
      )
      
      attachments = mail.body.attachments or []
      if attachments:
        existing_filenames = {row[0] for row in self._fetchall(cur, "SELECT filename FROM mail_attachments WHERE mail_id=%s AND user_id=%s", (mail.id, user_id))}
        for att in [a for a in attachments if a.filename not in existing_filenames]:
          cur.execute(
            """INSERT INTO mail_conn.mail_attachments
                         (mail_id, user_id, filename, content_type, content, size, cid)
                         VALUES (%s, %s, %s, %s, %s, %s, %s)""",
            (mail.id, user_id, att.filename, att.content_type, att.content, att.size, att.cid),
          )

      notify_new_mail(mail.id, mail.mail_thread_id, mail.mailbox_id, user_id)

  def get_mailbox(self, mailbox_id: str, user_id: str) -> Optional[MailBox]:
    """
    Return a MailBox for a user, or None if not found.
    """
    with self.transaction() as cur:
      cur.execute("SELECT id, name FROM mail_conn.mailboxes WHERE id=%s AND user_id=%s", (mailbox_id, user_id))
      mb_row = cur.fetchone()
      if not mb_row:
        return None
      cur.execute("SELECT id FROM mail_conn.mail_threads WHERE mailbox_id=%s AND user_id=%s", (mailbox_id, user_id))
      thread_ids = [r[0] for r in cur.fetchall()]

    threads = [t for t_id in thread_ids if (t := self.get_thread(t_id, user_id))]
    return MailBox(id=mb_row[0], name=mb_row[1], threads=threads)

  def get_thread(self, thread_id: str, user_id: str) -> Optional[MailThread]:
    """Retrieve a thread by ID for a user, including all mails."""
    with self.transaction(cursor_factory=RealDictCursor) as cur:
      cur.execute("SELECT id, mailbox_id FROM mail_conn.mail_threads WHERE id=%s AND user_id=%s", (thread_id, user_id))
      thread_row = cur.fetchone()
      if not thread_row:
        return None
      
      cur.execute(
        """SELECT * FROM mail_conn.mails WHERE mail_thread_id=%s AND user_id=%s ORDER BY thread_position""",
        (thread_id, user_id),
      )
      mail_rows = cur.fetchall()

    mails = []
    for row in mail_rows:
      attachments = self._get_attachments(row["id"], user_id)
      html_content = (row["body"] or {}).get("html", "<p>No content available</p>")
      mails.append(
        Mail(
          **row,
          body=MailBody(html=html_content, attachments=attachments),
          additional_metadata=row["additional_metadata"] or {}
        )
      )
    return MailThread(id=thread_id, mails=mails, mailbox_id=thread_row["mailbox_id"])

  def _get_attachments(self, mail_id: str, user_id: str) -> List[MailAttachment]:
    with self.transaction(cursor_factory=RealDictCursor) as cur:
      cur.execute(
        "SELECT filename, content_type, content, size, cid FROM mail_attachments WHERE mail_id=%s AND user_id=%s",
        (mail_id, user_id),
      )
      rows = cur.fetchall()

    return [
      MailAttachment(
        **r,
        content=r["content"].tobytes() if isinstance(r["content"], memoryview) else r["content"],
      )
      for r in rows
    ]

  def delete_thread(self, thread_id: str, user_id: str) -> bool:
    """Delete a thread and all associated mails/attachments for a user."""
    with self.transaction() as cur:
      cur.execute("SELECT id FROM mail_conn.mails WHERE mail_thread_id=%s AND user_id=%s", (thread_id, user_id))
      mail_ids = [m[0] for m in cur.fetchall()]

      if mail_ids:
          placeholders = ','.join(['%s'] * len(mail_ids))
          cur.execute(f"DELETE FROM mail_conn.mail_attachments WHERE mail_id IN ({placeholders}) AND user_id=%s", (*mail_ids, user_id))
          cur.execute(f"DELETE FROM mail_conn.mails WHERE id IN ({placeholders}) AND user_id=%s", (*mail_ids, user_id))
      
      cur.execute("DELETE FROM mail_conn.mail_threads WHERE id=%s AND user_id=%s", (thread_id, user_id))

    logger.info(f"User {user_id} deleted thread {thread_id} with {len(mail_ids)} mails")
    return True

  def get_threads_since(self, since_date: datetime, user_id: str) -> List[MailThread]:
    """
    Return a list of MailThreads for a user that have been updated since since_date.
    """
    with self.transaction() as cur:
      cur.execute("SELECT id FROM mail_conn.mail_threads WHERE last_updated >= %s AND user_id=%s", (since_date, user_id))
      results = [r[0] for r in cur.fetchall()]

    threads = [t for t_id in results if (t := self.get_thread(t_id, user_id))]
    logger.info(f"User {user_id} retrieved {len(threads)} threads since {since_date.isoformat()}")
    return threads

  def get_last_sync_date(self, user_id: str) -> Optional[datetime]:
    """
    Return the latest last_updated from a user's mail_threads, or None if none exist.
    """
    with self.transaction() as cur:
      cur.execute("SELECT MAX(last_updated) FROM mail_conn.mail_threads WHERE user_id=%s", (user_id,))
      row = cur.fetchone()
    if row and row[0]:
      return row[0]
    return None

  @staticmethod
  def _fetchall(cursor, query, params):
    cursor.execute(query, params)
    return cursor.fetchall()

  def reset_tables(self):
    """
    Truncate all tables to remove data while preserving the schema.
    """
    with self.transaction() as cur:
      logger.warning("Resetting all database tables. This will erase all email data.")
      cur.execute("""
        TRUNCATE TABLE 
          mail_conn.user_google_tokens,
          mail_conn.mail_attachments, 
          mail_conn.mails, 
          mail_conn.mail_threads, 
          mail_conn.mailboxes
        RESTART IDENTITY CASCADE;
      """)
      logger.info("Database tables reset successfully.")
