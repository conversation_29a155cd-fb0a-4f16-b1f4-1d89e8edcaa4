# SQL queries for database operations

SQL_CREATE_USER_GOOGLE_TOKENS_TABLE = """
CREATE TABLE IF NOT EXISTS mail_conn.user_google_tokens (
  user_id UUID PRIMARY KEY REFERENCES orchestrator.users(id) ON DELETE CASCADE,
  encrypted_refresh_token BYTEA NOT NULL,
  token_source VARCHAR,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);
"""

SQL_CREATE_MAILBOXES_TABLE = """
CREATE TABLE IF NOT EXISTS mail_conn.mailboxes (
  id VARCHAR NOT NULL,
  user_id UUID NOT NULL REFERENCES orchestrator.users(id) ON DELETE CASCADE,
  name VARCHAR NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  PRIMARY KEY (id, user_id)
);
"""

SQL_CREATE_MAIL_THREADS_TABLE = """
CREATE TABLE IF NOT EXISTS mail_conn.mail_threads (
  id VARCHAR NOT NULL,
  user_id UUID NOT NULL REFERENCES orchestrator.users(id) ON DELETE CASCADE,
  mailbox_id VARCHAR NOT NULL,
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  PRIMARY KEY (id, user_id),
  CONSTRAINT fk_mailbox
    FOREIGN KEY(mailbox_id, user_id)
    REFERENCES mail_conn.mailboxes(id, user_id)
);
"""

SQL_CREATE_MAILS_TABLE = """
CREATE TABLE IF NOT EXISTS mail_conn.mails (
  id VARCHAR NOT NULL,
  user_id UUID NOT NULL REFERENCES orchestrator.users(id) ON DELETE CASCADE,
  mail_thread_id VARCHAR NOT NULL,
  mailbox_id VARCHAR NOT NULL,
  subject TEXT NOT NULL,
  body JSONB NOT NULL,
  from_email TEXT NOT NULL,
  to_email TEXT NOT NULL,
  received_or_sent TEXT NOT NULL,
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
  thread_position INTEGER NOT NULL,
  additional_metadata JSONB DEFAULT '{}'::jsonb,
  PRIMARY KEY (id, user_id),
  CONSTRAINT fk_mail_thread
    FOREIGN KEY(mail_thread_id, user_id)
    REFERENCES mail_conn.mail_threads(id, user_id)
);
"""

SQL_CREATE_ATTACHMENTS_TABLE = """
CREATE TABLE IF NOT EXISTS mail_conn.mail_attachments (
  id SERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES orchestrator.users(id) ON DELETE CASCADE,
  mail_id VARCHAR NOT NULL,
  filename TEXT NOT NULL,
  content_type TEXT NOT NULL,
  content BYTEA NOT NULL,
  size INTEGER NOT NULL,
  cid TEXT,
  CONSTRAINT fk_mail
    FOREIGN KEY(mail_id, user_id)
    REFERENCES mail_conn.mails(id, user_id)
);
"""

# Indexes for performance
SQL_CREATE_INDEXES = [
  # User ID indexes
  "CREATE INDEX IF NOT EXISTS idx_mail_conn_mailboxes_user_id ON mail_conn.mailboxes(user_id);",
  "CREATE INDEX IF NOT EXISTS idx_mail_conn_mail_threads_user_id ON mail_conn.mail_threads(user_id);",
  "CREATE INDEX IF NOT EXISTS idx_mail_conn_mails_user_id ON mail_conn.mails(user_id);",
  "CREATE INDEX IF NOT EXISTS idx_mail_conn_mail_attachments_user_id ON mail_conn.mail_attachments(user_id);",

  # Existing indexes
  "CREATE INDEX IF NOT EXISTS idx_mail_conn_mail_threads_mailbox_id ON mail_conn.mail_threads(mailbox_id);",
  "CREATE INDEX IF NOT EXISTS idx_mail_conn_mails_mail_thread_id ON mail_conn.mails(mail_thread_id);",
  "CREATE INDEX IF NOT EXISTS idx_mail_conn_mails_timestamp ON mail_conn.mails(timestamp);",
  "CREATE INDEX IF NOT EXISTS idx_mail_conn_mail_attachments_mail_id ON mail_conn.mail_attachments(mail_id);",
]