import redis
from src.config import settings, logger

# Initialize Redis client with modern options
redis_client = redis.Redis(
  host=settings.REDIS_HOST, 
  port=settings.REDIS_PORT, 
  decode_responses=True,
  health_check_interval=30  # Periodically check connection health
)

def notify_orchestrator(event_type: str, payload: dict):
  """
  Publishes an event to the specified Redis stream.
  
  Args:
      event_type: Type of event (currently only 'new_mail' is used)
      payload: Dictionary with event data
  """
  stream_name = f"mail_events:{event_type}"
  try:
    # Using maxlen to set a trim policy and prevent the stream from growing indefinitely
    # This will trim the stream to approximately 10,000 entries when it grows too large
    message_id = redis_client.xadd(
      stream_name, 
      fields=payload,
      maxlen=10000,       # Keep around 10K messages
      approximate=True    # Allow Redis to optimize trimming
    )
    logger.info(f"📨 Published '{event_type}' event with ID {message_id}: {payload}")
  except redis.RedisError as e:
    logger.error(f"🚨 Failed to publish '{event_type}' event to Redis: {e}")

def notify_new_mail(mail_id: str, thread_id: str, mailbox_id: str, user_id: str):
  """
  Notifies orchestrator about a new mail in an existing thread for a specific user.
  """
  payload = {
    "mail_id": mail_id,
    "thread_id": thread_id,
    "mailbox_id": mailbox_id,
    "user_id": user_id
  }
  notify_orchestrator("new_mail", payload)

def notify_new_thread(thread_id: str, mailbox_id: str, user_id: str):
  """
  Notifies orchestrator about a new thread for a specific user.
  """
  payload = {
    "thread_id": thread_id, 
    "mailbox_id": mailbox_id,
    "user_id": user_id,
    "is_new_thread": "true"
  }
  notify_orchestrator("new_mail", payload)