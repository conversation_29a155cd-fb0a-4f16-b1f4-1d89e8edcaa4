import SuperTokens from "supertokens-auth-react";
import ThirdParty from "supertokens-auth-react/recipe/thirdparty";
import Session from "supertokens-auth-react/recipe/session";

export const superTokensConfig = {
  appInfo: {
    appName: "ClariQ",
    apiDomain: process.env.NEXT_PUBLIC_AUTH_URL || "http://localhost:4000",
    websiteDomain: process.env.NEXT_PUBLIC_WEBSITE_URL || "http://localhost:7777",
    apiBasePath: "/auth",
    websiteBasePath: "/auth",
  },
  recipeList: [
    ThirdParty.init({
      signInAndUpFeature: {
        providers: [
          ThirdParty.Google.init(),
        ],
      },
    }),
    Session.init(),
  ],
};

export const initSuperTokens = () => {
  SuperTokens.init(superTokensConfig);
};
