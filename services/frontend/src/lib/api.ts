import { ThreadRow, ThreadDetail } from "../types/thread";
import type { UnifiedThread } from "@/types/thread";
import { PAGINATION, API_ENDPOINTS } from "./constants";

/**
 * Fetches detailed information for a specific thread
 * @param threadId ID of the thread to fetch
 * @returns Promise containing the thread details
 */
export async function fetchThreadDetail(threadId: string, userId: string): Promise<ThreadDetail> {
  const API_BASE = process.env.NEXT_PUBLIC_API_URL ?? "";
  console.log(`Fetching thread detail for ${threadId} from ${API_BASE}/api/threads/${threadId}`);
  
  try {
    const response = await fetch(`${API_BASE}/api/threads/${threadId}?user_id=${userId}`);
    
    if (!response.ok) {
      const errorText = await response.text(); // Try to get error details
      console.error(`API error (${response.status}): ${errorText}`);
      throw new Error(`Failed to fetch thread detail: ${response.status} ${response.statusText}. Details: ${errorText.substring(0, 200)}`);
    }
    
    const data = await response.json();
    // Log the entire received data structure
    console.log("Full Thread Detail Data:", JSON.stringify(data, null, 2)); 
    console.log(`Successfully fetched thread ${threadId} with ${data.messages?.length || 0} messages`);
    return data;
  } catch (error) {
    console.error(`Error fetching thread ${threadId}:`, error);
    throw error;
  }
}

/**
 * Approves a response
 * @param responseId ID of the response to approve
 * @returns Promise containing the result of the approval
 */
export async function approveResponse(responseId: string, userId: string) {
  const API_BASE = process.env.NEXT_PUBLIC_API_URL ?? "";
  const response = await fetch(`${API_BASE}/api/responses/${responseId}/approve`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ user_id: userId }),
  });
  
  if (!response.ok) {
    throw new Error(`Failed to approve response: ${response.statusText}`);
  }
  
  return response.json();
}

/**
 * Rejects a response
 * @param responseId ID of the response to reject
 * @returns Promise containing the result of the rejection
 */
export async function rejectResponse(responseId: string, userId: string) {
  const API_BASE = process.env.NEXT_PUBLIC_API_URL ?? "";
  const response = await fetch(`${API_BASE}/api/responses/${responseId}/reject`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ user_id: userId }),
  });
  
  if (!response.ok) {
    throw new Error(`Failed to reject response: ${response.statusText}`);
  }
  
  return response.json();
}

/**
 * Sends a reply to an email thread
 * @param threadId ID of the thread
 * @param mailId ID of the email to reply to
 * @param replyText Text content of the reply
 * @param recipients Comma-separated recipient emails
 * @returns Promise containing the response from the server
 */
export async function sendMailReply(
  threadId: string,
  mailId: string,
  replyText: string,
  recipients: string,
  userId: string
) {
  const API_BASE = process.env.NEXT_PUBLIC_API_URL ?? "";
  
  const response = await fetch(`${API_BASE}/api/mail/send-reply`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      thread_id: threadId,
      mail_id: mailId,
      reply_text: replyText,
      recipients,
      user_id: userId
    }),
  });
  
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to send reply');
  }
  
  return response.json();
}

/**
 * Saves an email reply as a draft
 * @param threadId ID of the thread
 * @param mailId ID of the email to reply to
 * @param draftText Text content of the draft
 * @returns Promise containing the response from the server
 */
export async function saveMailDraft(threadId: string, mailId: string, draftText: string, userId: string) {
  const API_BASE = process.env.NEXT_PUBLIC_API_URL ?? "";
  
  const response = await fetch(`${API_BASE}/api/mail/save-draft`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      thread_id: threadId,
      mail_id: mailId,
      draft_text: draftText,
      user_id: userId
    }),
  });
  
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to save draft');
  }
  
  return response.json();
}

/**
 * Marks a thread as skipped
 * @param threadId ID of the thread to skip
 * @param reason Optional reason for skipping
 * @returns Promise containing the response from the server
 * 
 * DEPRECATED, remove
 */
export async function skipMail(threadId: string, userId: string, reason?: string) {
  const API_BASE = process.env.NEXT_PUBLIC_API_URL ?? "";
  
  const response = await fetch(`${API_BASE}/api/mail/skip`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      thread_id: threadId,
      reason,
      user_id: userId
    }),
  });
  
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to skip mail');
  }
  
  return response.json();
}

/**
 * Requests a regeneration of the draft reply
 * @param threadId ID of the thread
 * @param mailId ID of the email to regenerate a reply for
 * @returns Promise containing the response from the server
 * 
 * DEPRECATED, remove
 */
export async function regenerateDraft(threadId: string, mailId: string, userId: string) {
  const API_BASE = process.env.NEXT_PUBLIC_API_URL ?? "";
  
  const response = await fetch(`${API_BASE}/api/mail/regenerate-draft`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      thread_id: threadId,
      mail_id: mailId,
      user_id: userId
    }),
  });
  
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to regenerate draft');
  }
  
  return response.json();
}

/**
 * Fetches unified threads using React Query-compatible pattern
 * @param offset Starting offset for pagination  
 * @param limit Number of threads to fetch
 * @returns Promise containing an array of UnifiedThread objects
 */
export async function fetchUnifiedThreads(
  userId: string,
  offset: number = 0,
  limit: number = PAGINATION.LOAD_MORE_SIZE
): Promise<UnifiedThread[]> {
  const API_BASE = process.env.NEXT_PUBLIC_API_URL ?? "";
  
  const params = new URLSearchParams();
  params.append('limit', limit.toString());
  params.append('offset', offset.toString());
  params.append('user_id', userId);
  
  const url = `${API_BASE}${API_ENDPOINTS.THREADS_UNIFIED}?${params.toString()}`;
  const response = await fetch(url);
  
  if (!response.ok) {
    throw new Error(`Failed to fetch unified threads: ${response.status} ${response.statusText}`);
  }
  
  const data = await response.json();
  return data;
}

/**
 * Searches conversations using React Query-compatible pattern
 * @param query Search query string
 * @returns Promise containing search results
 */
export async function searchConversations(query: string, userId: string): Promise<any[]> {
  if (!query.trim()) return [];
  
  const API_BASE = process.env.NEXT_PUBLIC_API_URL ?? "";
  const url = `${API_BASE}${API_ENDPOINTS.SEARCH_CONVERSATIONS}?q=${encodeURIComponent(query)}&limit=${PAGINATION.SEARCH_RESULTS_LIMIT}&user_id=${userId}`;
  
  const response = await fetch(url);
  
  if (!response.ok) {
    throw new Error(`Failed to search conversations: ${response.status} ${response.statusText}`);
  }
  
  const data = await response.json();
  return Array.isArray(data) ? data : [];
}

/**
 * Searches mails using React Query-compatible pattern  
 * @param query Search query string
 * @returns Promise containing search results
 */
export async function searchMails(query: string): Promise<any[]> {
  if (!query.trim()) return [];
  
  const API_BASE = process.env.NEXT_PUBLIC_API_URL ?? "";
  const url = `${API_BASE}${API_ENDPOINTS.SEARCH_MAILS}?q=${encodeURIComponent(query)}&limit=${PAGINATION.SEARCH_RESULTS_LIMIT}`;
  
  const response = await fetch(url);
  
  if (!response.ok) {
    throw new Error(`Failed to search mails: ${response.status} ${response.statusText}`);
  }
  
  const data = await response.json();
  return Array.isArray(data) ? data : [];
}