import { useInfiniteQ<PERSON>y, useQ<PERSON>y, InfiniteData, useQueryClient } from "@tanstack/react-query";
import { fetchThreadDetail, fetchUnifiedThreads } from "./api";
import { ThreadRow } from "@/types/thread";
import { useState, useEffect, useCallback, useMemo } from 'react'
import type { ResponseView, UnifiedThread } from '@/types/thread'
import { PAGINATION, API_ENDPOINTS, FILTERS } from './constants'

/**
 * Hook to fetch a single thread's details with intelligent caching
 * @param threadId The ID of the thread to fetch
 */
export const useThreadDetail = (threadId: string, userId: string) => {
  return useQuery({
    queryKey: ["thread-detail", threadId, userId], // User-specific key
    queryFn: () => fetchThreadDetail(threadId, userId),
    enabled: !!threadId && !!userId, // Depends on both threadId and userId
    staleTime: 2 * 60 * 1000, // 2 minutes - recently viewed threads stay fresh
    gcTime: 10 * 60 * 1000, // 10 minutes - keep in cache for navigation
    refetchOnMount: false, // Don't refetch if data is fresh
    refetchOnWindowFocus: false, // Don't refetch on focus
    retry: (failureCount, error) => {
      // Don't retry on 404 (thread not found)
      if (error?.message?.includes('404')) return false
      return failureCount < 2
    },
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

/**
 * Hook for prefetching thread details on hover or other interactions
 * Enables instant loading when user actually opens the thread
 */
export function useThreadDetailPrefetch() {
  const queryClient = useQueryClient()
  
  const prefetchThreadDetail = useCallback((threadId: string, userId: string) => {
    if (!threadId) return
    
    queryClient.prefetchQuery({
      queryKey: ['thread-detail', threadId, userId],
      queryFn: () => fetchThreadDetail(threadId, userId),
      staleTime: 2 * 60 * 1000, // Same as main hook
    })
  }, [queryClient])
  
  return { prefetchThreadDetail }
}

/**
 * Hook for invalidating thread detail cache after updates
 * Ensures cache consistency when thread data changes
 */
export function useInvalidateThreadDetail() {
  const queryClient = useQueryClient()
  
  const invalidateThreadDetail = useCallback((threadId: string) => {
    if (!threadId) return
    
    queryClient.invalidateQueries({
      queryKey: ['thread-detail', threadId]
    })
  }, [queryClient])
  
  const invalidateAllThreadDetails = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: ['thread-detail']
    })
  }, [queryClient])
  
  return { invalidateThreadDetail, invalidateAllThreadDetails }
}

/**
 * @deprecated This hook uses manual state management. Use `useUnifiedThreadsWithQuery` instead for better performance and caching.
 * Hook for fetching unified threads with optional filtering
 * This replaces the need for separate useThreads and useResponses hooks
 */
export function useUnifiedThreads(
  userId: string,
  readStatus?: 'read' | 'unread' | 'all',
  responseStatus?: 'pending' | 'completed' | 'all'
) {
  const [threads, setThreads] = useState<UnifiedThread[]>([])
  const [loading, setLoading] = useState(true)
  const [loadingMore, setLoadingMore] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [hasMore, setHasMore] = useState(true)
  const [offset, setOffset] = useState(0)

  const fetchThreads = async (isLoadMore: boolean = false) => {
    try {
      if (isLoadMore) {
        setLoadingMore(true)
      } else {
        setLoading(true)
        setOffset(0)
      }
      setError(null)
      
      const params = new URLSearchParams()
      params.append('limit', PAGINATION.LOAD_MORE_SIZE.toString())
      params.append('offset', (isLoadMore ? offset : 0).toString())
      
      if (readStatus && readStatus !== 'all') {
        params.append('read_status', readStatus)
      }
      if (responseStatus && responseStatus !== 'all') {
        params.append('response_status', responseStatus)
      }

      if (userId) {
        params.append('user_id', userId);
      }
      
      const url = `${API_ENDPOINTS.THREADS_UNIFIED}?${params.toString()}`
      const response = await fetch(url)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch threads: ${response.statusText}`)
      }
      
      const data = await response.json()
      
      if (isLoadMore) {
        setThreads(prev => [...prev, ...data])
        setOffset(prev => prev + data.length)
      } else {
        setThreads(data)
        setOffset(data.length)
      }
      
      // Check if there are more threads to load
      setHasMore(data.length === PAGINATION.LOAD_MORE_SIZE)
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
      console.error('Error fetching unified threads:', err)
    } finally {
      setLoading(false)
      setLoadingMore(false)
    }
  }

  useEffect(() => {
    fetchThreads(false)
  }, [readStatus, responseStatus])

  const loadMore = () => {
    if (!loadingMore && hasMore) {
      fetchThreads(true)
    }
  }

  const refetch = () => {
    fetchThreads(false)
  }

  return { 
    threads, 
    loading, 
    loadingMore,
    error, 
    hasMore,
    loadMore,
    refetch
  }
}

/**
 * @deprecated This hook uses manual state management and fetches all threads upfront. Use `useUnifiedThreadsWithQuery` instead.
 * Optimized hook that loads threads progressively and filters client-side
 * This eliminates API calls on filter changes while maintaining pagination
 */
export function useUnifiedThreadsOptimized(userId: string) {
  const [allThreads, setAllThreads] = useState<UnifiedThread[]>([])
  const [loading, setLoading] = useState(true)
  const [loadingMore, setLoadingMore] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [hasMore, setHasMore] = useState(true)
  const [offset, setOffset] = useState(0)

  // Load initial threads on mount
  useEffect(() => {
    const fetchInitialThreads = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const params = new URLSearchParams()
        params.append('limit', PAGINATION.INITIAL_LOAD_SIZE.toString())
        params.append('offset', '0')
        // No filtering - get ALL threads for client-side filtering
        if (userId) {
          params.append('user_id', userId);
        }
        
        const url = `${API_ENDPOINTS.THREADS_UNIFIED}?${params.toString()}`
        const response = await fetch(url)
        
        if (!response.ok) {
          throw new Error(`Failed to fetch threads: ${response.statusText}`)
        }
        
        const data = await response.json()
        setAllThreads(data)
        setOffset(data.length)
        setHasMore(data.length === PAGINATION.INITIAL_LOAD_SIZE)
        
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error')
        console.error('Error fetching initial threads:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchInitialThreads()
  }, []) // Only run once on mount

  // Load more threads function
  const loadMore = useCallback(async () => {
    if (loadingMore || !hasMore) return

    try {
      setLoadingMore(true)
      setError(null)
      
      const params = new URLSearchParams()
      params.append('limit', PAGINATION.LOAD_MORE_SIZE.toString())
      params.append('offset', offset.toString())
      // No filtering - get ALL threads for client-side filtering
      if (userId) {
        params.append('user_id', userId);
      }
      
      const url = `${API_ENDPOINTS.THREADS_UNIFIED}?${params.toString()}`
      const response = await fetch(url)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch more threads: ${response.statusText}`)
      }
      
      const data = await response.json()
      setAllThreads(prev => [...prev, ...data])
      setOffset(prev => prev + data.length)
      setHasMore(data.length === PAGINATION.LOAD_MORE_SIZE)
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
      console.error('Error fetching more threads:', err)
    } finally {
      setLoadingMore(false)
    }
  }, [loadingMore, hasMore, offset])

  // Client-side filtering function
  const getFilteredThreads = useCallback((
    readStatus?: 'read' | 'unread' | 'all',
    responseStatus?: 'pending' | 'completed' | 'all'
  ): UnifiedThread[] => {
    let filtered = allThreads

    // Apply read status filter
    if (readStatus && readStatus !== 'all') {
      filtered = filtered.filter(thread => {
        if (readStatus === 'read') return thread.is_read === true
        if (readStatus === 'unread') return thread.is_read === false
        return true
      })
    }

    // Apply response status filter
    if (responseStatus && responseStatus !== 'all') {
      filtered = filtered.filter(thread => {
        if (responseStatus === 'pending') {
          // Handle both 'pending' and 'pending_review' statuses, plus threads without responses
          return !thread.response_status || 
                 thread.response_status === 'pending' || 
                 thread.response_status === 'pending_review'
        }
        if (responseStatus === 'completed') {
          // Handle both 'completed' and 'sent' statuses
          return thread.response_status === 'completed' || 
                 thread.response_status === 'sent'
        }
        return true
      })
    }

    return filtered
  }, [allThreads])

  const refetch = useCallback(() => {
    // Reset and reload from the beginning
    setAllThreads([])
    setOffset(0)
    setHasMore(true)
    setLoading(true)
    
    // This will trigger the useEffect to reload
    window.location.reload() // Simple approach for now
  }, [])

  return {
    allThreads,
    getFilteredThreads,
    loading,
    loadingMore,
    error,
    hasMore,
    loadMore,
    refetch
  }
}

/**
 * React Query-based hook for unified threads with intelligent caching
 * Replaces manual state management with React Query's optimized data fetching
 */
export function useUnifiedThreadsWithQuery(userId: string) {
  const query = useInfiniteQuery({
    queryKey: ['unified-threads', userId],
    queryFn: ({ pageParam = 0 }) => fetchUnifiedThreads(userId, pageParam, PAGINATION.LOAD_MORE_SIZE),
    getNextPageParam: (lastPage, allPages) => {
      // If last page has full limit, there might be more
      if (lastPage.length === PAGINATION.LOAD_MORE_SIZE) {
        return allPages.length * PAGINATION.LOAD_MORE_SIZE;
      }
      return undefined;
    },
    initialPageParam: 0,
    staleTime: 5 * 60 * 1000, // 5 minutes - data stays fresh
    gcTime: 30 * 60 * 1000, // 30 minutes - keep in cache (was cacheTime)
    refetchOnWindowFocus: false, // Don't refetch on window focus
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors, but retry on 5xx and network errors
      if (error?.message?.includes('4')) return false;
      return failureCount < 2;
    },
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  // Flatten all pages into a single array
  const allThreads = useMemo(() => {
    return query.data?.pages.flatMap(page => page) || [];
  }, [query.data]);

  // Client-side filtering function for instant filter switching
  const getFilteredThreads = useCallback((
    readStatus?: 'read' | 'unread' | 'all',
    responseStatus?: 'pending' | 'completed' | 'all'
  ): UnifiedThread[] => {
    let filtered: UnifiedThread[] = allThreads;

    // Apply read status filter
    if (readStatus && readStatus !== FILTERS.READ_STATUS.ALL) {
      filtered = filtered.filter((thread: UnifiedThread) => {
        if (readStatus === FILTERS.READ_STATUS.READ) return thread.is_read === true;
        if (readStatus === FILTERS.READ_STATUS.UNREAD) return thread.is_read === false;
        return true;
      });
    }

    // Apply response status filter  
    if (responseStatus && responseStatus !== FILTERS.RESPONSE_STATUS.ALL) {
      filtered = filtered.filter((thread: UnifiedThread) => {
        if (responseStatus === FILTERS.RESPONSE_STATUS.PENDING) {
          // Handle both 'pending' and 'pending_review' statuses, plus threads without responses
          return !thread.response_status || 
                 thread.response_status === 'pending' || 
                 thread.response_status === 'pending_review';
        }
        if (responseStatus === FILTERS.RESPONSE_STATUS.COMPLETED) {
          // Handle both 'completed' and 'sent' statuses
          return thread.response_status === 'completed' || 
                 thread.response_status === 'sent';
        }
        return true;
      });
    }

    return filtered;
  }, [allThreads]);

  return {
    // Core data
    allThreads,
    getFilteredThreads,
    
    // React Query states
    isLoading: query.isLoading,
    isLoadingMore: query.isFetchingNextPage,
    error: query.error,
    hasMore: query.hasNextPage,
    
    // Actions
    loadMore: query.fetchNextPage,
    refetch: query.refetch,
    
    // Additional React Query states for advanced usage
    isStale: query.isStale,
    isFetching: query.isFetching,
    dataUpdatedAt: query.dataUpdatedAt,
    
    // For debugging and monitoring
    query // Expose full query object for advanced usage
  };
} 