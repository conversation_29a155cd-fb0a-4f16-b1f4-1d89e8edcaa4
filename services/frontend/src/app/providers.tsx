"use client";

import { useState, createContext, useContext, useMemo } from "react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useEventSource } from "@/lib/useEventSource";
import { SuperTokensWrapper } from "supertokens-auth-react";
import { initSuperTokens } from "@/config/supertokens";
import { GlobalResizeObserverProvider } from "@/lib/useGlobalResizeObserver";

initSuperTokens();

// Create context for SSE connection status
const EventSourceContext = createContext<{
  status: 'connecting' | 'open' | 'closed' | 'error';
}>({
  status: 'closed',
});

// Hook to use the EventSource context
export const useEventSourceStatus = () => useContext(EventSourceContext);

interface AppProvidersProps {
  children: React.ReactNode;
}

/**
 * A provider that sets up the QueryClient, EventSource connection
 * and shows connection status
 */
export default function AppProviders({ children }: AppProvidersProps) {
  // Create a QueryClient with stable identity
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 1000 * 60 * 5, // 5 minutes
        refetchOnWindowFocus: false,
      },
    },
  }));

  return (
    <SuperTokensWrapper>
      <QueryClientProvider client={queryClient}>
        <GlobalResizeObserverProvider>
          <EventSourceProvider>
            {children}
          </EventSourceProvider>
        </GlobalResizeObserverProvider>
      </QueryClientProvider>
    </SuperTokensWrapper>
  );
}

/**
 * A provider that sets up the EventSource connection
 * and shows connection status
 */
function EventSourceProvider({ children }: { children: React.ReactNode }) {
  const [status, setStatus] = useState<'connecting' | 'open' | 'closed' | 'error'>("connecting");
  
  // Set up EventSource with exponential backoff
  useEventSource({
    onStatusChange: setStatus,
    initialBackoff: 1000,  // Start with 1s backoff
    maxBackoff: 30000,     // Max 30s backoff
  });
  
  // Create a stable context value
  const contextValue = useMemo(() => ({ status }), [status]);
  
  return (
    <EventSourceContext.Provider value={contextValue}>
      {children}
    </EventSourceContext.Provider>
  );
} 