'use client'

import './globals.css'
import { Inter } from 'next/font/google'
import { AppNavbar } from '../components/AppNavbar'
import { useEffect, useCallback, useRef, useReducer, useMemo } from 'react'
import AppProviders from './providers'
import { ChatSidebar, ChatSidebarRef } from '../components/chat'
import CommandPalette from '../components/ui/CommandPalette'
import ErrorBoundary from '../components/ui/ErrorBoundary'
import { useCommandPalette } from '../hooks/useCommandPalette'
import { ChatSidebarProvider } from '../hooks/useChatSidebar'
import * as Tooltip from '@radix-ui/react-tooltip'
import { ChatBubbleIcon, ViewVerticalIcon } from '@radix-ui/react-icons'
import { clsx } from 'clsx';
import { SessionAuth } from 'supertokens-auth-react/recipe/session';

const inter = Inter({ subsets: ['latin'] })

// Consolidated layout state interface
interface LayoutState {
  isClient: boolean
  isChatOpen: boolean
  isMobileView: boolean
  chatWidth: number
  isDragging: boolean
  selectedConversation: {id: string, title?: string} | null
  isNavCollapsed: boolean
}

// Layout state actions
type LayoutAction =
  | { type: 'SET_CLIENT'; payload: boolean }
  | { type: 'SET_CHAT_OPEN'; payload: boolean }
  | { type: 'SET_MOBILE_VIEW'; payload: boolean }
  | { type: 'SET_CHAT_WIDTH'; payload: number }
  | { type: 'SET_DRAGGING'; payload: boolean }
  | { type: 'SET_SELECTED_CONVERSATION'; payload: {id: string, title?: string} | null }
  | { type: 'TOGGLE_CHAT' }
  | { type: 'SET_NAV_COLLAPSED'; payload: boolean }
  | { type: 'BATCH_UPDATE'; payload: Partial<LayoutState> }

// Initial layout state
const initialLayoutState: LayoutState = {
  isClient: false,
  isChatOpen: false,
  isMobileView: false,
  chatWidth: 400,
  isDragging: false,
  selectedConversation: null,
  isNavCollapsed: false
}

// Layout state reducer
function layoutReducer(state: LayoutState, action: LayoutAction): LayoutState {
  switch (action.type) {
    case 'SET_CLIENT':
      return { ...state, isClient: action.payload }
    case 'SET_CHAT_OPEN':
      return { ...state, isChatOpen: action.payload }
    case 'SET_MOBILE_VIEW':
      return { ...state, isMobileView: action.payload }
    case 'SET_CHAT_WIDTH':
      return { ...state, chatWidth: action.payload }
    case 'SET_DRAGGING':
      return { ...state, isDragging: action.payload }
    case 'SET_SELECTED_CONVERSATION':
      return { ...state, selectedConversation: action.payload }
    case 'SET_NAV_COLLAPSED':
      return { ...state, isNavCollapsed: action.payload }
    case 'TOGGLE_CHAT':
      return { ...state, isChatOpen: !state.isChatOpen }
    case 'BATCH_UPDATE':
      return { ...state, ...action.payload }
    default:
      return state
  }
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Consolidated layout state using useReducer
  const [layoutState, dispatch] = useReducer(layoutReducer, initialLayoutState)
  const { isClient, isChatOpen, isMobileView, chatWidth, isDragging, selectedConversation, isNavCollapsed } = layoutState
  
  const chatSidebarRef = useRef<ChatSidebarRef | null>(null)
  
  // Command palette state
  const { isOpen: isCommandPaletteOpen, closePalette } = useCommandPalette()
  
  // Memoize chat constraints to prevent recalculation
  const chatConstraints = useMemo(() => ({
    minWidth: 253,
    maxWidth: 753
  }), [])
  
  // Helper function to clamp chat width to constraints
  const clampChatWidth = useCallback((width: number) => {
    return Math.max(chatConstraints.minWidth, Math.min(chatConstraints.maxWidth, width))
  }, [chatConstraints])
  
  // Handle conversation selection from command palette
  const handleConversationSelect = useCallback((conversationId: string, title?: string) => {
    console.log('🎯 Command palette selected conversation:', conversationId, title)
    
    // Batch update state to prevent multiple re-renders
    dispatch({
      type: 'BATCH_UPDATE',
      payload: {
        isChatOpen: true,
        selectedConversation: { id: conversationId, title }
      }
    })
  }, [])
  
  // Save conversation to localStorage when it changes
  const saveLastConversation = useCallback((conversationId: string, title?: string) => {
    if (typeof window !== 'undefined') {
      if (conversationId) {
        const conversationData = { id: conversationId, title }
        localStorage.setItem('lastChatConversation', JSON.stringify(conversationData))
        console.log('💾 Saved last conversation:', conversationData)
      } else {
        // Clear saved conversation when starting new chat
        localStorage.removeItem('lastChatConversation')
        console.log('🗑️ Cleared last conversation')
      }
    }
  }, [])

  // Load last conversation from localStorage
  const loadLastConversation = useCallback(() => {
    if (typeof window !== 'undefined') {
      try {
        const saved = localStorage.getItem('lastChatConversation')
        if (saved) {
          const conversationData = JSON.parse(saved)
          return conversationData
        }
      } catch (error) {
        console.error('Error loading last conversation:', error)
      }
    }
    return null
  }, [])

  // Effect to handle conversation switching when chat opens or conversation changes
  useEffect(() => {
    if (isChatOpen && selectedConversation && chatSidebarRef.current) {
      console.log('🔄 Switching to conversation from command palette:', selectedConversation)
      chatSidebarRef.current.switchToConversation(selectedConversation.id, selectedConversation.title)
      // Save this conversation as the last one
      saveLastConversation(selectedConversation.id, selectedConversation.title)
      // Clear the selection after switching
      dispatch({ type: 'SET_SELECTED_CONVERSATION', payload: null })
    }
  }, [isChatOpen, selectedConversation, saveLastConversation])

  // Effect to restore last conversation when chat opens (if no specific conversation selected)
  useEffect(() => {
    if (isChatOpen && !selectedConversation && chatSidebarRef.current && isClient) {
      const lastConversation = loadLastConversation()
      if (lastConversation) {
        console.log('🔄 Restoring last conversation (optimized):', lastConversation)
        // Small delay to ensure chat sidebar is fully rendered
        setTimeout(() => {
          // First check if we're already on this conversation to avoid redundant calls
          if (chatSidebarRef.current) {
            console.log('⚡ Optimized restoration - checking if conversation needs loading')
            chatSidebarRef.current.switchToConversation(lastConversation.id, lastConversation.title)
          }
        }, 100)
      }
    }
  }, [isChatOpen, selectedConversation, isClient, loadLastConversation])
  
  useEffect(() => {
    // Initialize client state and set up event listeners
    const handleKeyDown = (e: KeyboardEvent) => {
      // Check for cmd+L (or ctrl+L for Windows)
      if ((e.metaKey || e.ctrlKey) && e.key === 'l') {
        e.preventDefault() // Prevent default browser behavior
        dispatch({ type: 'TOGGLE_CHAT' })
      }
    }
    
    const handleResize = () => {
      // 768px is the default md breakpoint in Tailwind
      const isMobile = window.innerWidth < 768
      dispatch({ type: 'SET_MOBILE_VIEW', payload: isMobile })
    }
    
    // Batch initial setup to prevent multiple re-renders
    handleResize()
    dispatch({ type: 'SET_CLIENT', payload: true })
    
    window.addEventListener('keydown', handleKeyDown)
    window.addEventListener('resize', handleResize)
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown)
      window.removeEventListener('resize', handleResize)
    }
  }, [])

// Update keyboard shortcuts to Cmd+B for nav and Cmd+L for chat
useEffect(() => {
  const handleKeyDown = (e) => {
    if ((e.metaKey || e.ctrlKey) && e.key.toLowerCase() === 'b') {
      e.preventDefault();
      dispatch({ type: 'SET_NAV_COLLAPSED', payload: !isNavCollapsed });
    }
    if ((e.metaKey || e.ctrlKey) && e.key.toLowerCase() === 'l') {
      e.preventDefault();
      dispatch({ type: 'SET_CHAT_OPEN', payload: !isChatOpen });
    }
  };

  window.addEventListener('keydown', handleKeyDown);
  return () => window.removeEventListener('keydown', handleKeyDown);
}, [isNavCollapsed, isChatOpen]);

// Chat drag handlers with proper context integration
const chatDragStateRef = useRef({ 
  isDragging: false, 
  startX: 0,
  startWidth: 400
})

const handleChatMouseDown = useCallback((e: React.MouseEvent) => {
  e.preventDefault()
  chatDragStateRef.current.isDragging = true
  chatDragStateRef.current.startX = e.clientX
  chatDragStateRef.current.startWidth = chatWidth
  dispatch({ type: 'SET_DRAGGING', payload: true })
}, [chatWidth])

const handleChatMouseMove = useCallback((e: MouseEvent) => {
  if (!chatDragStateRef.current.isDragging) return
  
  const delta = chatDragStateRef.current.startX - e.clientX
  const newWidth = chatDragStateRef.current.startWidth + delta
  const clampedWidth = Math.max(chatConstraints.minWidth, Math.min(chatConstraints.maxWidth, newWidth))
  
  dispatch({ type: 'SET_CHAT_WIDTH', payload: clampedWidth })
}, [chatConstraints])

const handleChatMouseUp = useCallback(() => {
  if (!chatDragStateRef.current.isDragging) return
  chatDragStateRef.current.isDragging = false
  dispatch({ type: 'SET_DRAGGING', payload: false })
}, [])

useEffect(() => {
  if (isDragging) {
    document.addEventListener('mousemove', handleChatMouseMove)
    document.addEventListener('mouseup', handleChatMouseUp)
    document.body.style.cursor = 'col-resize'
    document.body.style.userSelect = 'none'
    
    return () => {
      document.removeEventListener('mousemove', handleChatMouseMove)
      document.removeEventListener('mouseup', handleChatMouseUp)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }
  }
}, [isDragging, handleChatMouseMove, handleChatMouseUp])

// Main content style accounts for chat width
const mainContentStyle = useMemo(() => ({
  width: isChatOpen && !isMobileView ? `calc(100% - ${chatWidth + 7}px)` : '100%'
}), [isChatOpen, isMobileView, chatWidth])

  return (
    <html lang="en" suppressHydrationWarning className="light">
      <body className={`min-h-screen bg-gray-100 text-[var(--foreground)] ${inter.className}`} style={{background: '#f3f4f6'}}>
        <AppProviders>
          <ChatSidebarProvider 
            isOpen={isChatOpen} 
            width={chatWidth} 
            isMobileView={isMobileView}
            setChatWidth={(width: number) => dispatch({ type: 'SET_CHAT_WIDTH', payload: clampChatWidth(width) })}
          >
            <div className="flex flex-col md:flex-row min-h-screen relative">
            {/* Fixed sidebar */}
            {!isNavCollapsed && (
              <AppNavbar />
            )}
            
            {/* Main content wrapper with 360 border and rounded corners - slimmer margins */}
            <div 
              className="flex-1 m-2 bg-gray-100 rounded-lg border border-[var(--border)] overflow-hidden relative"
              style={{
                height: isMobileView ? 'calc(100vh - 16px)' : 'calc(100vh - 40px)', // Slimmer margins
              }}
            >
              {/* White content area inside the gray container */}
              <div className="bg-white rounded-lg h-full overflow-hidden relative flex flex-row">
                <div 
                  data-main-content
                  className="h-full overflow-hidden"
                  style={mainContentStyle}
                >
                  <main className="h-full py-0 px-0 max-w-none w-full mx-auto overflow-hidden">
                    <SessionAuth>
                      {children}
                    </SessionAuth>
                  </main>
                </div>
                
                {isChatOpen && (
                  <>
                    <div
                      className={clsx(
                        "splitter",
                        isDragging && "dragging"
                      )}
                      onMouseDown={handleChatMouseDown}
                    />
                    <div 
                      data-chat-panel
                      className="bg-[var(--background)] overflow-hidden"
                      style={{
                        width: `${chatWidth}px`,
                        height: '100%',
                        flexShrink: 0
                      }}
                    >
                      <div className="h-full flex flex-col">
                        <div className="flex-1 overflow-hidden">
                          <ChatSidebar 
                            width={chatWidth} 
                            ref={chatSidebarRef}
                            onConversationChange={saveLastConversation}
                          />
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
          
          {/* New slim bottom status bar for toggles */}
          {!isMobileView && (
            <Tooltip.Provider>
              <div className="fixed bottom-0 left-0 right-0 h-6 bg-gray-100 border-t border-gray-200 flex items-center justify-between px-4 z-30">
                {/* Left toggle for nav sidebar */}
                <Tooltip.Root>
                  <Tooltip.Trigger asChild>
                    <button
                      onClick={() => dispatch({ type: 'SET_NAV_COLLAPSED', payload: !isNavCollapsed })}
                      className="p-1 rounded-sm transition-all duration-200 hover:scale-105 flex items-center"
                      aria-label={isNavCollapsed ? 'Expand navigation' : 'Collapse navigation'}
                    >
                      <ViewVerticalIcon className={`w-4 h-4 transition-colors duration-200 ${
                        isNavCollapsed ? 'text-gray-700' : 'text-gray-400'
                      }`} />
                      <span className="ml-1 text-gray-400 font-mono text-xs">⌘B</span>
                    </button>
                  </Tooltip.Trigger>
                  <Tooltip.Portal>
                    <Tooltip.Content className="bg-gray-900 text-white px-2 py-1 rounded text-xs" side="top">
                      {isNavCollapsed ? 'Expand Nav' : 'Collapse Nav'}
                      <span className="ml-2 text-gray-400 font-mono">⌘[</span>
                    </Tooltip.Content>
                  </Tooltip.Portal>
                </Tooltip.Root>
                
                {/* Empty center space for future elements */}
                <div className="flex-1" />
                
                {/* Right toggle for chat sidebar */}
                <Tooltip.Root>
                  <Tooltip.Trigger asChild>
                    <button
                      onClick={() => dispatch({ type: 'SET_CHAT_OPEN', payload: !isChatOpen })}
                      className="p-1 rounded-sm transition-all duration-200 hover:scale-105 flex items-center"
                      aria-label={isChatOpen ? 'Collapse chat' : 'Expand chat'}
                    >
                      <ChatBubbleIcon className={`w-4 h-4 transition-colors duration-200 ${
                        isChatOpen ? 'text-gray-700' : 'text-gray-400'
                      }`} />
                      <span className="ml-1 text-gray-400 font-mono text-xs">⌘L</span>
                    </button>
                  </Tooltip.Trigger>
                  <Tooltip.Portal>
                    <Tooltip.Content className="bg-gray-900 text-white px-2 py-1 rounded text-xs" side="top">
                      {isChatOpen ? 'Collapse Chat' : 'Expand Chat'}
                      <span className="ml-2 text-gray-400 font-mono">⌘]</span>
                    </Tooltip.Content>
                  </Tooltip.Portal>
                </Tooltip.Root>
              </div>
            </Tooltip.Provider>
          )}
          
          {/* Command Palette */}
          <ErrorBoundary fallback={<div className="fixed inset-0 bg-black/20 z-50 flex items-center justify-center"><div className="bg-white p-4 rounded-lg shadow-lg">Command palette error</div></div>}>
            <CommandPalette
              isOpen={isCommandPaletteOpen}
              onClose={closePalette}
              onConversationSelect={handleConversationSelect}
            />
          </ErrorBoundary>
          
          {/* DevTools only mounted client-side and in development */}
          {isClient && process.env.NODE_ENV !== 'production' && (
            <div id="react-query-devtools-container" />
          )}
        </ChatSidebarProvider>
      </AppProviders>
    </body>
  </html>
  );
}