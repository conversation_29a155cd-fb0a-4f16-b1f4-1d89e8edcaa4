import { useState, useCallback, useMemo, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useUnifiedThreadsWithQuery, useThreadDetail } from '@/lib/useThreads'
import { useThreadModal } from '@/hooks/useThreadModal'
import { useThreadNavigation } from './shared/thread_detail/hooks/useThreadNavigation'
import { convertThreadDetailToViewData } from './shared/thread_detail/utils/formatting'
import { useChatSidebar } from '@/hooks/useChatSidebar';
import { useSessionContext } from 'supertokens-auth-react/recipe/session';

export function useMailListData() {
  const session = useSessionContext();
  const userId = session.loading ? undefined : session.userId;
  const router = useRouter()
  const searchParams = useSearchParams()
  const [activeFilter, setActiveFilter] = useState(searchParams.get('filter') || 'all')
  const [searchQuery, setSearchQuery] = useState('')

  const { isOpen, currentThreadId, openThread, closeThread, navigateToThread } = useThreadModal()

  const {
    data: threadDetail,
    isLoading: modalLoading,
    error: threadDetailError,
    isFetching: isBackgroundRefreshing
  } = useThreadDetail(currentThreadId || '', userId)

  const modalThread = useMemo(() => {
    if (!threadDetail || !currentThreadId) return null
    if (threadDetail.thread_id !== currentThreadId) return null
    try { return convertThreadDetailToViewData(threadDetail) } catch { return null }
  }, [threadDetail, currentThreadId])

  const modalError = threadDetailError ? (threadDetailError instanceof Error ? threadDetailError.message : 'Failed to load thread') : null

  const {
    getFilteredThreads,
    isLoading: loading,
    isLoadingMore: loadingMore,
    error,
    hasMore,
    loadMore,
    refetch
  } = useUnifiedThreadsWithQuery(userId)

  const getFilterParams = (filter: string) => {
    switch (filter) {
      case 'received':
        return { readStatus: 'all' as const, responseStatus: 'pending' as const }
      case 'sent':
        return { readStatus: 'all' as const, responseStatus: 'completed' as const }
      default:
        return { readStatus: 'all' as const, responseStatus: 'all' as const }
    }
  }

  const filterParams = getFilterParams(activeFilter)
  const threads = useMemo(
    () => getFilteredThreads(filterParams.readStatus, filterParams.responseStatus),
    [getFilteredThreads, filterParams.readStatus, filterParams.responseStatus]
  )

  const filteredThreads = useMemo(() => {
    if (!searchQuery) return threads
    const searchLower = searchQuery.toLowerCase()
    return threads.filter(t =>
      t.subject?.toLowerCase().includes(searchLower) ||
      t.from_address?.toLowerCase().includes(searchLower) ||
      t.body_html?.toLowerCase().includes(searchLower) ||
      t.ai_summary?.toLowerCase().includes(searchLower) ||
      t.topic_labels.some(label => label.toLowerCase().includes(searchLower))
    )
  }, [threads, searchQuery])

  const threadIds = filteredThreads.map(t => t.thread_id)
  const navigation = useThreadNavigation(currentThreadId || '', threadIds, navigateToThread)

  // Ensure threads load correctly after refresh
  useEffect(() => {
    if (!loading && threads.length === 0) {
      refetch()
    }
  }, [loading, threads.length, refetch])

  const handleFilterChange = useCallback((filter: string) => {
    setActiveFilter(filter)
    const params = new URLSearchParams(searchParams.toString())
    params.set('filter', filter)
    router.replace(`/mails?${params.toString()}`)
  }, [router, searchParams])

  const handleSearch = useCallback((q: string) => setSearchQuery(q), [])

  const handleNavigate = useCallback((dir: 'prev' | 'next') => {
    if (dir === 'prev') navigation.navigateToPrev()
    else navigation.navigateToNext()
  }, [navigation])

  useEffect(() => {
    const urlFilter = searchParams.get('filter')
    if (urlFilter && urlFilter !== activeFilter) setActiveFilter(urlFilter)
  }, [searchParams, activeFilter])

  const { isMobileView } = useChatSidebar()
  const isDesktopThreadOpen = !isMobileView && isOpen

  return {
    activeFilter,
    searchQuery,
    handleFilterChange,
    handleSearch,
    openThread,
    closeThread,
    navigation,
    handleNavigate,
    threads: filteredThreads,
    loading,
    loadingMore,
    hasMore,
    loadMore,
    error,
    isOpen,
    modalThread,
    modalLoading,
    modalError,
    isBackgroundRefreshing,
    currentThreadId,
    isDesktopThreadOpen,
    isMobileView
  }
}
