{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-visually-hidden": "^1.2.3", "@tanstack/react-query": "^5.74.4", "@tanstack/react-query-devtools": "^5.74.4", "@tiptap/extension-link": "^2.11.7", "@tiptap/extension-placeholder": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@types/crypto-js": "^4.2.2", "clsx": "^2.1.1", "cmdk": "^1.1.1", "crypto-js": "^4.2.0", "isomorphic-dompurify": "^2.25.0", "juice": "^11.0.1", "lettersanitizer": "^1.0.7", "lucide-react": "^0.525.0", "next": "15.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-use": "^17.6.0", "remark-gfm": "^4.0.1", "supertokens-auth-react": "^0.49.1", "tailwind-merge": "^3.2.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5"}}