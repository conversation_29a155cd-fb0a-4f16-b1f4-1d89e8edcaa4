{"version": 3, "names": ["_helperCreateRegexpFeaturesPlugin", "require", "_helper<PERSON>lugin<PERSON><PERSON>s", "_default", "exports", "default", "declare", "api", "assertVersion", "createRegExpFeaturePlugin", "name", "feature", "manipulateOptions", "opts", "parserOpts", "plugins", "push"], "sources": ["../src/index.ts"], "sourcesContent": ["/* eslint-disable @babel/development/plugin-name */\nimport { createRegExpFeaturePlugin } from \"@babel/helper-create-regexp-features-plugin\";\nimport { declare } from \"@babel/helper-plugin-utils\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  return createRegExpFeaturePlugin({\n    name: \"transform-unicode-sets-regex\",\n    feature: \"unicodeSetsFlag\",\n    manipulateOptions(opts, parserOpts) {\n      if (!process.env.BABEL_8_BREAKING) {\n        // @ts-ignore(Babel 7 vs Babel 8) This plugin has been removed\n        parserOpts.plugins.push(\"regexpUnicodeSets\");\n      }\n    },\n  });\n});\n"], "mappings": ";;;;;;AACA,IAAAA,iCAAA,GAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAD,OAAA;AAAqD,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEtC,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,uCAAoB,CAAC;EAEtC,OAAO,IAAAC,2DAAyB,EAAC;IAC/BC,IAAI,EAAE,8BAA8B;IACpCC,OAAO,EAAE,iBAAiB;IAC1BC,iBAAiBA,CAACC,IAAI,EAAEC,UAAU,EAAE;MACC;QAEjCA,UAAU,CAACC,OAAO,CAACC,IAAI,CAAC,mBAAmB,CAAC;MAC9C;IACF;EACF,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}