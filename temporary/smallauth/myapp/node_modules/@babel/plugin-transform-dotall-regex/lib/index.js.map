{"version": 3, "names": ["_helperCreateRegexpFeaturesPlugin", "require", "_helper<PERSON>lugin<PERSON><PERSON>s", "_default", "exports", "default", "declare", "api", "assertVersion", "createRegExpFeaturePlugin", "name", "feature"], "sources": ["../src/index.ts"], "sourcesContent": ["/* eslint-disable @babel/development/plugin-name */\nimport { createRegExpFeaturePlugin } from \"@babel/helper-create-regexp-features-plugin\";\nimport { declare } from \"@babel/helper-plugin-utils\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  return createRegExpFeaturePlugin({\n    name: \"transform-dotall-regex\",\n    feature: \"dotAllFlag\",\n  });\n});\n"], "mappings": ";;;;;;AACA,IAAAA,iCAAA,GAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAD,OAAA;AAAqD,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEtC,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,uCAAoB,CAAC;EAEtC,OAAO,IAAAC,2DAAyB,EAAC;IAC/BC,IAAI,EAAE,wBAAwB;IAC9BC,OAAO,EAAE;EACX,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}