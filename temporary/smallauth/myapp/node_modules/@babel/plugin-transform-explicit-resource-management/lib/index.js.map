{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_pluginTransformDestructuring", "_core", "isAnonymousFunctionDefinition", "node", "t", "isArrowFunctionExpression", "isFunctionExpression", "isClassExpression", "id", "emitSetFunctionNameCall", "state", "expression", "name", "callExpression", "addHelper", "stringLiteral", "_default", "exports", "default", "declare", "api", "assertVersion", "TOP_LEVEL_USING", "Map", "isUsingDeclaration", "isVariableDeclaration", "kind", "has", "transformUsingDeclarationsVisitor", "ForOfStatement", "path", "left", "declarations", "tmpId", "scope", "generateUidIdentifierBasedOnNode", "ensureBlock", "unshiftForXStatementBody", "variableDeclaration", "variableDeclarator", "cloneNode", "BlockStatement|StaticBlock", "ctx", "needsAwait", "body", "generateUidIdentifier", "isAwaitUsing", "get", "delete", "decl", "currentInit", "init", "memberExpression", "identifier", "isIdentifier", "disposeCall", "replacement", "template", "statement", "ast", "awaitExpression", "inherits", "parentPath", "isFunction", "isTryStatement", "isCatchClause", "replaceWith", "blockStatement", "isStaticBlock", "transformUsingDeclarationsVisitorSkipFn", "traverse", "visitors", "merge", "Function", "skip", "manipulateOptions", "_", "p", "plugins", "push", "visitor", "Program", "clear", "sourceType", "some", "innerBlockBody", "stmt", "isFunctionDeclaration", "isImportDeclaration", "<PERSON><PERSON><PERSON><PERSON>", "isExportDefaultDeclaration", "declaration", "varId", "isClassDeclaration", "toExpression", "isExpression", "exportNamedDeclaration", "exportSpecifier", "isExportNamedDeclaration", "Object", "keys", "getOuterBindingIdentifiers", "map", "isExportDeclaration", "set", "remove", "pushContainer", "async"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { unshiftForXStatementBody } from \"@babel/plugin-transform-destructuring\";\nimport { types as t, template, traverse } from \"@babel/core\";\nimport type { NodePath, Visitor, PluginPass } from \"@babel/core\";\n\nconst enum USING_KIND {\n  NORMAL,\n  AWAIT,\n}\n\n// https://tc39.es/ecma262/#sec-isanonymousfunctiondefinition\nfunction isAnonymousFunctionDefinition(\n  node: t.Node,\n): node is\n  | t.ClassExpression\n  | t.ArrowFunctionExpression\n  | t.FunctionExpression {\n  return (\n    t.isArrowFunctionExpression(node) ||\n    ((t.isFunctionExpression(node) || t.isClassExpression(node)) && !node.id)\n  );\n}\n\nfunction emitSetFunctionNameCall(\n  state: PluginPass,\n  expression: t.Expression,\n  name: string,\n) {\n  return t.callExpression(state.addHelper(\"setFunctionName\"), [\n    expression,\n    t.stringLiteral(name),\n  ]);\n}\n\nexport default declare(api => {\n  // The first Babel 7 version with usingCtx helper support.\n  api.assertVersion(REQUIRED_VERSION(\"^7.23.9\"));\n\n  const TOP_LEVEL_USING = new Map<t.Node, USING_KIND>();\n\n  function isUsingDeclaration(node: t.Node): node is t.VariableDeclaration {\n    if (!t.isVariableDeclaration(node)) return false;\n    return (\n      node.kind === \"using\" ||\n      node.kind === \"await using\" ||\n      TOP_LEVEL_USING.has(node)\n    );\n  }\n\n  const transformUsingDeclarationsVisitor: Visitor<PluginPass> = {\n    ForOfStatement(path: NodePath<t.ForOfStatement>) {\n      const { left } = path.node;\n      if (!isUsingDeclaration(left)) return;\n\n      const { id } = left.declarations[0];\n      const tmpId = path.scope.generateUidIdentifierBasedOnNode(id);\n      left.declarations[0].id = tmpId;\n      left.kind = \"const\";\n\n      path.ensureBlock();\n      unshiftForXStatementBody(path, [\n        t.variableDeclaration(\"using\", [\n          t.variableDeclarator(id, t.cloneNode(tmpId)),\n        ]),\n      ]);\n    },\n    \"BlockStatement|StaticBlock\"(\n      path: NodePath<t.BlockStatement | t.StaticBlock>,\n      state,\n    ) {\n      let ctx: t.Identifier | null = null;\n      let needsAwait = false;\n      const scope = path.scope;\n\n      for (const node of path.node.body) {\n        if (!isUsingDeclaration(node)) continue;\n        ctx ??= scope.generateUidIdentifier(\"usingCtx\");\n        const isAwaitUsing =\n          node.kind === \"await using\" ||\n          TOP_LEVEL_USING.get(node) === USING_KIND.AWAIT;\n        needsAwait ||= isAwaitUsing;\n\n        if (!TOP_LEVEL_USING.delete(node)) {\n          node.kind = \"const\";\n        }\n        for (const decl of node.declarations) {\n          const currentInit = decl.init;\n          decl.init = t.callExpression(\n            t.memberExpression(\n              t.cloneNode(ctx),\n              isAwaitUsing ? t.identifier(\"a\") : t.identifier(\"u\"),\n            ),\n            [\n              isAnonymousFunctionDefinition(currentInit) &&\n              t.isIdentifier(decl.id)\n                ? emitSetFunctionNameCall(state, currentInit, decl.id.name)\n                : currentInit,\n            ],\n          );\n        }\n      }\n      if (!ctx) return;\n\n      const disposeCall = t.callExpression(\n        t.memberExpression(t.cloneNode(ctx), t.identifier(\"d\")),\n        [],\n      );\n\n      const replacement = template.statement.ast`\n        try {\n          var ${t.cloneNode(ctx)} = ${state.addHelper(\"usingCtx\")}();\n          ${path.node.body}\n        } catch (_) {\n          ${t.cloneNode(ctx)}.e = _;\n        } finally {\n          ${needsAwait ? t.awaitExpression(disposeCall) : disposeCall}\n        }\n      ` as t.TryStatement;\n\n      t.inherits(replacement, path.node);\n\n      const { parentPath } = path;\n      if (\n        parentPath.isFunction() ||\n        parentPath.isTryStatement() ||\n        parentPath.isCatchClause()\n      ) {\n        path.replaceWith(t.blockStatement([replacement]));\n      } else if (path.isStaticBlock()) {\n        path.node.body = [replacement];\n      } else {\n        path.replaceWith(replacement);\n      }\n    },\n  };\n\n  const transformUsingDeclarationsVisitorSkipFn: Visitor<PluginPass> =\n    traverse.visitors.merge([\n      transformUsingDeclarationsVisitor,\n      {\n        Function(path) {\n          path.skip();\n        },\n      },\n    ]);\n\n  return {\n    name: \"transform-explicit-resource-management\",\n    manipulateOptions: (_, p) => p.plugins.push(\"explicitResourceManagement\"),\n\n    visitor: traverse.visitors.merge([\n      transformUsingDeclarationsVisitor,\n      {\n        // To transform top-level using declarations, we must wrap the\n        // module body in a block after hoisting all the exports and imports.\n        // This might cause some variables to be `undefined` rather than TDZ.\n        Program(path) {\n          TOP_LEVEL_USING.clear();\n\n          if (path.node.sourceType !== \"module\") return;\n          if (!path.node.body.some(isUsingDeclaration)) return;\n\n          const innerBlockBody = [];\n          for (const stmt of path.get(\"body\")) {\n            if (stmt.isFunctionDeclaration() || stmt.isImportDeclaration()) {\n              continue;\n            }\n\n            let node: t.Statement | t.Declaration = stmt.node;\n            let shouldRemove = true;\n\n            if (stmt.isExportDefaultDeclaration()) {\n              let { declaration } = stmt.node;\n              let varId;\n              if (t.isClassDeclaration(declaration)) {\n                varId = declaration.id;\n                // Move the class id to the var binding such that the scope binding\n                // info is correct. Then we clone one to ensure inner class reference\n                // will stay same if the outer binding is mutated.\n                declaration.id = t.cloneNode(varId);\n                declaration = t.toExpression(declaration);\n              } else if (!t.isExpression(declaration)) {\n                continue;\n              }\n\n              varId ??= path.scope.generateUidIdentifier(\"_default\");\n              innerBlockBody.push(\n                t.variableDeclaration(\"var\", [\n                  t.variableDeclarator(varId, declaration),\n                ]),\n              );\n              stmt.replaceWith(\n                t.exportNamedDeclaration(null, [\n                  t.exportSpecifier(\n                    t.cloneNode(varId),\n                    t.identifier(\"default\"),\n                  ),\n                ]),\n              );\n              continue;\n            }\n\n            if (stmt.isExportNamedDeclaration()) {\n              node = stmt.node.declaration;\n              if (!node || t.isFunction(node)) continue;\n\n              stmt.replaceWith(\n                t.exportNamedDeclaration(\n                  null,\n                  Object.keys(t.getOuterBindingIdentifiers(node, false)).map(\n                    id => t.exportSpecifier(t.identifier(id), t.identifier(id)),\n                  ),\n                ),\n              );\n              shouldRemove = false;\n            } else if (stmt.isExportDeclaration()) {\n              continue;\n            }\n\n            if (t.isClassDeclaration(node)) {\n              const { id } = node;\n              node.id = t.cloneNode(id);\n              innerBlockBody.push(\n                t.variableDeclaration(\"var\", [\n                  t.variableDeclarator(id, t.toExpression(node)),\n                ]),\n              );\n            } else if (t.isVariableDeclaration(node)) {\n              if (node.kind === \"using\") {\n                TOP_LEVEL_USING.set(stmt.node, USING_KIND.NORMAL);\n              } else if (node.kind === \"await using\") {\n                TOP_LEVEL_USING.set(stmt.node, USING_KIND.AWAIT);\n              }\n              node.kind = \"var\";\n              innerBlockBody.push(node);\n            } else {\n              innerBlockBody.push(stmt.node);\n            }\n\n            if (shouldRemove) stmt.remove();\n          }\n\n          path.pushContainer(\"body\", t.blockStatement(innerBlockBody));\n        },\n        // We must transform `await using` in async functions before that\n        // async-to-generator will transform `await` expressions into `yield`\n        Function(path, state) {\n          if (path.node.async) {\n            path.traverse(transformUsingDeclarationsVisitorSkipFn, state);\n          }\n        },\n      },\n    ]),\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,6BAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AASA,SAASG,6BAA6BA,CACpCC,IAAY,EAIW;EACvB,OACEC,WAAC,CAACC,yBAAyB,CAACF,IAAI,CAAC,IAChC,CAACC,WAAC,CAACE,oBAAoB,CAACH,IAAI,CAAC,IAAIC,WAAC,CAACG,iBAAiB,CAACJ,IAAI,CAAC,KAAK,CAACA,IAAI,CAACK,EAAG;AAE7E;AAEA,SAASC,uBAAuBA,CAC9BC,KAAiB,EACjBC,UAAwB,EACxBC,IAAY,EACZ;EACA,OAAOR,WAAC,CAACS,cAAc,CAACH,KAAK,CAACI,SAAS,CAAC,iBAAiB,CAAC,EAAE,CAC1DH,UAAU,EACVP,WAAC,CAACW,aAAa,CAACH,IAAI,CAAC,CACtB,CAAC;AACJ;AAAC,IAAAI,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEc,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAE5BA,GAAG,CAACC,aAAa,sCAA4B,CAAC;EAE9C,MAAMC,eAAe,GAAG,IAAIC,GAAG,CAAqB,CAAC;EAErD,SAASC,kBAAkBA,CAACrB,IAAY,EAAiC;IACvE,IAAI,CAACC,WAAC,CAACqB,qBAAqB,CAACtB,IAAI,CAAC,EAAE,OAAO,KAAK;IAChD,OACEA,IAAI,CAACuB,IAAI,KAAK,OAAO,IACrBvB,IAAI,CAACuB,IAAI,KAAK,aAAa,IAC3BJ,eAAe,CAACK,GAAG,CAACxB,IAAI,CAAC;EAE7B;EAEA,MAAMyB,iCAAsD,GAAG;IAC7DC,cAAcA,CAACC,IAAgC,EAAE;MAC/C,MAAM;QAAEC;MAAK,CAAC,GAAGD,IAAI,CAAC3B,IAAI;MAC1B,IAAI,CAACqB,kBAAkB,CAACO,IAAI,CAAC,EAAE;MAE/B,MAAM;QAAEvB;MAAG,CAAC,GAAGuB,IAAI,CAACC,YAAY,CAAC,CAAC,CAAC;MACnC,MAAMC,KAAK,GAAGH,IAAI,CAACI,KAAK,CAACC,gCAAgC,CAAC3B,EAAE,CAAC;MAC7DuB,IAAI,CAACC,YAAY,CAAC,CAAC,CAAC,CAACxB,EAAE,GAAGyB,KAAK;MAC/BF,IAAI,CAACL,IAAI,GAAG,OAAO;MAEnBI,IAAI,CAACM,WAAW,CAAC,CAAC;MAClB,IAAAC,sDAAwB,EAACP,IAAI,EAAE,CAC7B1B,WAAC,CAACkC,mBAAmB,CAAC,OAAO,EAAE,CAC7BlC,WAAC,CAACmC,kBAAkB,CAAC/B,EAAE,EAAEJ,WAAC,CAACoC,SAAS,CAACP,KAAK,CAAC,CAAC,CAC7C,CAAC,CACH,CAAC;IACJ,CAAC;IACD,4BAA4BQ,CAC1BX,IAAgD,EAChDpB,KAAK,EACL;MACA,IAAIgC,GAAwB,GAAG,IAAI;MACnC,IAAIC,UAAU,GAAG,KAAK;MACtB,MAAMT,KAAK,GAAGJ,IAAI,CAACI,KAAK;MAExB,KAAK,MAAM/B,IAAI,IAAI2B,IAAI,CAAC3B,IAAI,CAACyC,IAAI,EAAE;QACjC,IAAI,CAACpB,kBAAkB,CAACrB,IAAI,CAAC,EAAE;QAC/BuC,GAAG,WAAHA,GAAG,GAAHA,GAAG,GAAKR,KAAK,CAACW,qBAAqB,CAAC,UAAU,CAAC;QAC/C,MAAMC,YAAY,GAChB3C,IAAI,CAACuB,IAAI,KAAK,aAAa,IAC3BJ,eAAe,CAACyB,GAAG,CAAC5C,IAAI,CAAC,MAAqB;QAChDwC,UAAU,KAAVA,UAAU,GAAKG,YAAY;QAE3B,IAAI,CAACxB,eAAe,CAAC0B,MAAM,CAAC7C,IAAI,CAAC,EAAE;UACjCA,IAAI,CAACuB,IAAI,GAAG,OAAO;QACrB;QACA,KAAK,MAAMuB,IAAI,IAAI9C,IAAI,CAAC6B,YAAY,EAAE;UACpC,MAAMkB,WAAW,GAAGD,IAAI,CAACE,IAAI;UAC7BF,IAAI,CAACE,IAAI,GAAG/C,WAAC,CAACS,cAAc,CAC1BT,WAAC,CAACgD,gBAAgB,CAChBhD,WAAC,CAACoC,SAAS,CAACE,GAAG,CAAC,EAChBI,YAAY,GAAG1C,WAAC,CAACiD,UAAU,CAAC,GAAG,CAAC,GAAGjD,WAAC,CAACiD,UAAU,CAAC,GAAG,CACrD,CAAC,EACD,CACEnD,6BAA6B,CAACgD,WAAW,CAAC,IAC1C9C,WAAC,CAACkD,YAAY,CAACL,IAAI,CAACzC,EAAE,CAAC,GACnBC,uBAAuB,CAACC,KAAK,EAAEwC,WAAW,EAAED,IAAI,CAACzC,EAAE,CAACI,IAAI,CAAC,GACzDsC,WAAW,CAEnB,CAAC;QACH;MACF;MACA,IAAI,CAACR,GAAG,EAAE;MAEV,MAAMa,WAAW,GAAGnD,WAAC,CAACS,cAAc,CAClCT,WAAC,CAACgD,gBAAgB,CAAChD,WAAC,CAACoC,SAAS,CAACE,GAAG,CAAC,EAAEtC,WAAC,CAACiD,UAAU,CAAC,GAAG,CAAC,CAAC,EACvD,EACF,CAAC;MAED,MAAMG,WAAW,GAAGC,cAAQ,CAACC,SAAS,CAACC,GAAG;AAChD;AACA,gBAAgBvD,WAAC,CAACoC,SAAS,CAACE,GAAG,CAAC,MAAMhC,KAAK,CAACI,SAAS,CAAC,UAAU,CAAC;AACjE,YAAYgB,IAAI,CAAC3B,IAAI,CAACyC,IAAI;AAC1B;AACA,YAAYxC,WAAC,CAACoC,SAAS,CAACE,GAAG,CAAC;AAC5B;AACA,YAAYC,UAAU,GAAGvC,WAAC,CAACwD,eAAe,CAACL,WAAW,CAAC,GAAGA,WAAW;AACrE;AACA,OAAyB;MAEnBnD,WAAC,CAACyD,QAAQ,CAACL,WAAW,EAAE1B,IAAI,CAAC3B,IAAI,CAAC;MAElC,MAAM;QAAE2D;MAAW,CAAC,GAAGhC,IAAI;MAC3B,IACEgC,UAAU,CAACC,UAAU,CAAC,CAAC,IACvBD,UAAU,CAACE,cAAc,CAAC,CAAC,IAC3BF,UAAU,CAACG,aAAa,CAAC,CAAC,EAC1B;QACAnC,IAAI,CAACoC,WAAW,CAAC9D,WAAC,CAAC+D,cAAc,CAAC,CAACX,WAAW,CAAC,CAAC,CAAC;MACnD,CAAC,MAAM,IAAI1B,IAAI,CAACsC,aAAa,CAAC,CAAC,EAAE;QAC/BtC,IAAI,CAAC3B,IAAI,CAACyC,IAAI,GAAG,CAACY,WAAW,CAAC;MAChC,CAAC,MAAM;QACL1B,IAAI,CAACoC,WAAW,CAACV,WAAW,CAAC;MAC/B;IACF;EACF,CAAC;EAED,MAAMa,uCAA4D,GAChEC,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAAC,CACtB5C,iCAAiC,EACjC;IACE6C,QAAQA,CAAC3C,IAAI,EAAE;MACbA,IAAI,CAAC4C,IAAI,CAAC,CAAC;IACb;EACF,CAAC,CACF,CAAC;EAEJ,OAAO;IACL9D,IAAI,EAAE,wCAAwC;IAC9C+D,iBAAiB,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACC,OAAO,CAACC,IAAI,CAAC,4BAA4B,CAAC;IAEzEC,OAAO,EAAEV,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAAC,CAC/B5C,iCAAiC,EACjC;MAIEqD,OAAOA,CAACnD,IAAI,EAAE;QACZR,eAAe,CAAC4D,KAAK,CAAC,CAAC;QAEvB,IAAIpD,IAAI,CAAC3B,IAAI,CAACgF,UAAU,KAAK,QAAQ,EAAE;QACvC,IAAI,CAACrD,IAAI,CAAC3B,IAAI,CAACyC,IAAI,CAACwC,IAAI,CAAC5D,kBAAkB,CAAC,EAAE;QAE9C,MAAM6D,cAAc,GAAG,EAAE;QACzB,KAAK,MAAMC,IAAI,IAAIxD,IAAI,CAACiB,GAAG,CAAC,MAAM,CAAC,EAAE;UACnC,IAAIuC,IAAI,CAACC,qBAAqB,CAAC,CAAC,IAAID,IAAI,CAACE,mBAAmB,CAAC,CAAC,EAAE;YAC9D;UACF;UAEA,IAAIrF,IAAiC,GAAGmF,IAAI,CAACnF,IAAI;UACjD,IAAIsF,YAAY,GAAG,IAAI;UAEvB,IAAIH,IAAI,CAACI,0BAA0B,CAAC,CAAC,EAAE;YACrC,IAAI;cAAEC;YAAY,CAAC,GAAGL,IAAI,CAACnF,IAAI;YAC/B,IAAIyF,KAAK;YACT,IAAIxF,WAAC,CAACyF,kBAAkB,CAACF,WAAW,CAAC,EAAE;cACrCC,KAAK,GAAGD,WAAW,CAACnF,EAAE;cAItBmF,WAAW,CAACnF,EAAE,GAAGJ,WAAC,CAACoC,SAAS,CAACoD,KAAK,CAAC;cACnCD,WAAW,GAAGvF,WAAC,CAAC0F,YAAY,CAACH,WAAW,CAAC;YAC3C,CAAC,MAAM,IAAI,CAACvF,WAAC,CAAC2F,YAAY,CAACJ,WAAW,CAAC,EAAE;cACvC;YACF;YAEAC,KAAK,WAALA,KAAK,GAALA,KAAK,GAAK9D,IAAI,CAACI,KAAK,CAACW,qBAAqB,CAAC,UAAU,CAAC;YACtDwC,cAAc,CAACN,IAAI,CACjB3E,WAAC,CAACkC,mBAAmB,CAAC,KAAK,EAAE,CAC3BlC,WAAC,CAACmC,kBAAkB,CAACqD,KAAK,EAAED,WAAW,CAAC,CACzC,CACH,CAAC;YACDL,IAAI,CAACpB,WAAW,CACd9D,WAAC,CAAC4F,sBAAsB,CAAC,IAAI,EAAE,CAC7B5F,WAAC,CAAC6F,eAAe,CACf7F,WAAC,CAACoC,SAAS,CAACoD,KAAK,CAAC,EAClBxF,WAAC,CAACiD,UAAU,CAAC,SAAS,CACxB,CAAC,CACF,CACH,CAAC;YACD;UACF;UAEA,IAAIiC,IAAI,CAACY,wBAAwB,CAAC,CAAC,EAAE;YACnC/F,IAAI,GAAGmF,IAAI,CAACnF,IAAI,CAACwF,WAAW;YAC5B,IAAI,CAACxF,IAAI,IAAIC,WAAC,CAAC2D,UAAU,CAAC5D,IAAI,CAAC,EAAE;YAEjCmF,IAAI,CAACpB,WAAW,CACd9D,WAAC,CAAC4F,sBAAsB,CACtB,IAAI,EACJG,MAAM,CAACC,IAAI,CAAChG,WAAC,CAACiG,0BAA0B,CAAClG,IAAI,EAAE,KAAK,CAAC,CAAC,CAACmG,GAAG,CACxD9F,EAAE,IAAIJ,WAAC,CAAC6F,eAAe,CAAC7F,WAAC,CAACiD,UAAU,CAAC7C,EAAE,CAAC,EAAEJ,WAAC,CAACiD,UAAU,CAAC7C,EAAE,CAAC,CAC5D,CACF,CACF,CAAC;YACDiF,YAAY,GAAG,KAAK;UACtB,CAAC,MAAM,IAAIH,IAAI,CAACiB,mBAAmB,CAAC,CAAC,EAAE;YACrC;UACF;UAEA,IAAInG,WAAC,CAACyF,kBAAkB,CAAC1F,IAAI,CAAC,EAAE;YAC9B,MAAM;cAAEK;YAAG,CAAC,GAAGL,IAAI;YACnBA,IAAI,CAACK,EAAE,GAAGJ,WAAC,CAACoC,SAAS,CAAChC,EAAE,CAAC;YACzB6E,cAAc,CAACN,IAAI,CACjB3E,WAAC,CAACkC,mBAAmB,CAAC,KAAK,EAAE,CAC3BlC,WAAC,CAACmC,kBAAkB,CAAC/B,EAAE,EAAEJ,WAAC,CAAC0F,YAAY,CAAC3F,IAAI,CAAC,CAAC,CAC/C,CACH,CAAC;UACH,CAAC,MAAM,IAAIC,WAAC,CAACqB,qBAAqB,CAACtB,IAAI,CAAC,EAAE;YACxC,IAAIA,IAAI,CAACuB,IAAI,KAAK,OAAO,EAAE;cACzBJ,eAAe,CAACkF,GAAG,CAAClB,IAAI,CAACnF,IAAI,GAAmB,CAAC;YACnD,CAAC,MAAM,IAAIA,IAAI,CAACuB,IAAI,KAAK,aAAa,EAAE;cACtCJ,eAAe,CAACkF,GAAG,CAAClB,IAAI,CAACnF,IAAI,GAAkB,CAAC;YAClD;YACAA,IAAI,CAACuB,IAAI,GAAG,KAAK;YACjB2D,cAAc,CAACN,IAAI,CAAC5E,IAAI,CAAC;UAC3B,CAAC,MAAM;YACLkF,cAAc,CAACN,IAAI,CAACO,IAAI,CAACnF,IAAI,CAAC;UAChC;UAEA,IAAIsF,YAAY,EAAEH,IAAI,CAACmB,MAAM,CAAC,CAAC;QACjC;QAEA3E,IAAI,CAAC4E,aAAa,CAAC,MAAM,EAAEtG,WAAC,CAAC+D,cAAc,CAACkB,cAAc,CAAC,CAAC;MAC9D,CAAC;MAGDZ,QAAQA,CAAC3C,IAAI,EAAEpB,KAAK,EAAE;QACpB,IAAIoB,IAAI,CAAC3B,IAAI,CAACwG,KAAK,EAAE;UACnB7E,IAAI,CAACwC,QAAQ,CAACD,uCAAuC,EAAE3D,KAAK,CAAC;QAC/D;MACF;IACF,CAAC,CACF;EACH,CAAC;AACH,CAAC,CAAC", "ignoreList": []}