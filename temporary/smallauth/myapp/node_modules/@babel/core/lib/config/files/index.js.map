{"version": 3, "names": ["_package", "require", "_configuration", "_plugins"], "sources": ["../../../src/config/files/index.ts"], "sourcesContent": ["type indexBrowserType = typeof import(\"./index-browser\");\ntype indexType = typeof import(\"./index\");\n\n// Kind of gross, but essentially asserting that the exports of this module are the same as the\n// exports of index-browser, since this file may be replaced at bundle time with index-browser.\n({}) as any as indexBrowserType as indexType;\n\nexport { findPackageData } from \"./package.ts\";\n\nexport {\n  findConfigUpwards,\n  findRelativeConfig,\n  findRootConfig,\n  loadConfig,\n  resolveShowConfigPath,\n  ROOT_CONFIG_FILENAMES,\n} from \"./configuration.ts\";\nexport type {\n  ConfigFile,\n  IgnoreFile,\n  RelativeConfig,\n  FilePackageData,\n} from \"./types.ts\";\nexport {\n  loadPlugin,\n  loadPreset,\n  resolvePlugin,\n  resolvePreset,\n} from \"./plugins.ts\";\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAAA,QAAA,GAAAC,OAAA;AAEA,IAAAC,cAAA,GAAAD,OAAA;AAcA,IAAAE,QAAA,GAAAF,OAAA;AAlBA,CAAC,CAAC,CAAC;AAA0C", "ignoreList": []}