# ClariQ User Authentication & Multi-Tenant Refactor Plan

## Executive Summary
This plan refactors the ClariQ microservice architecture to support secure multi-tenant user authentication, ensuring all data (chats, emails, search results) is strictly scoped to authenticated users. We'll implement SuperTokens-based authentication with encrypted OAuth token storage following Option C from the design notes.

## Current State Analysis (Updated)
✅ **Already Implemented**:
- **Database Schema**: User isolation is fully implemented in both `mail_conn` and `orchestrator` schemas with proper user_id foreign keys
- **Search Service**: Has complete SuperTokens integration and user_id filtering in both API and Qdrant backend
- **Agent Service**: Has SuperTokens initialization and middleware setup
- **Frontend**: SuperTokens configuration exists with SessionAuth wrapper protecting all routes
- **Temporary Auth**: Working SuperTokens implementation with Google OAuth in `temporary/smallauth/`

❌ **Missing/Incomplete**:
- **No dedicated auth service** - still using temporary implementation
- **Orchestrator Service**: Missing SuperTokens middleware and user context extraction
- **Mail Connection Service**: Missing SuperTokens integration, still uses token.json files
- **Frontend**: Missing auth routes (no /auth pages), API calls don't include auth headers
- **Service-to-service authentication** - no JWT forwarding between services
- **Token storage system** - mail_connection needs encrypted OAuth token storage

## Phase 1: Authentication Service Foundation

### 1.1 Create Dedicated Auth Service ⚠️ PRIORITY
**Status**: Not started - currently using temporary implementation
**Location**: `services/auth/`
**Actions**:
- Migrate SuperTokens from `temporary/smallauth/` to production-ready service
- Configure Google OAuth with proper scopes for Gmail access (already configured in temporary)
- Set up session management with JWT tokens
- Create user registration/login endpoints

**Implementation Details**:
```yaml
# services/auth/docker-compose.yml
services:
  auth:
    build: .
    ports:
      - "4000:4000"
    environment:
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - SUPERTOKENS_CONNECTION_URI=http://supertokens:3567
```

### 1.2 Database Schema Status ✅ COMPLETE
**Status**: Already implemented with full user isolation
**Current State**:
- `orchestrator.users` table exists with proper structure
- All tables in both `mail_conn` and `orchestrator` schemas have user_id foreign keys
- Proper indexes are in place for user-scoped queries
- Foreign key constraints ensure data integrity

**Verification**: Check `services/orchestrator/src/db/connection.py` and `services/mail_connection/src/db/statements.py`

### 1.3 Encrypted Token Storage ⚠️ NEEDS IMPLEMENTATION
**Status**: Table structure exists but encryption logic missing
**Location**: `services/mail_connection/`
**Current**: Table `mail_conn.user_google_tokens` already defined in schema
**Missing**:
- Encryption/decryption utilities
- Token exchange endpoint
- Background worker for token refresh

**Encryption Strategy**:
- Use Fernet symmetric encryption with environment-based key
- Store encryption key in Docker secrets/environment
- Implement token rotation and refresh logic

## Phase 2: Service-by-Service Refactoring

### 2.1 Orchestrator Service Updates ⚠️ HIGH PRIORITY
**Status**: Missing SuperTokens middleware integration
**Current State**: No authentication middleware, endpoints don't extract user context
**Files to modify**:
- `services/orchestrator/src/api.py`: Add SuperTokens middleware
- `services/orchestrator/src/controllers/api_controller.py`: Update endpoints to extract user_id
- All database queries already support user_id filtering

**Required Changes**:
```python
# Add to services/orchestrator/src/api.py
from supertokens_python import init, InputAppInfo, SupertokensConfig
from supertokens_python.recipe import session
from supertokens_python.framework.fastapi import get_middleware

init(
    app_info=InputAppInfo(
        app_name="ClariQ Orchestrator",
        api_domain="http://localhost:8003",
        website_domain="http://localhost:7777",
        api_base_path="/api/v1/auth",
    ),
    supertokens_config=SupertokensConfig(
        connection_uri="http://localhost:3567"
    ),
    framework="fastapi",
    recipe_list=[session.init()]
)

app.add_middleware(get_middleware())
```

### 2.2 Mail Connection Service Refactoring ⚠️ HIGH PRIORITY
**Status**: No authentication, still uses token.json files
**Current State**: No SuperTokens integration, no user context
**Major Changes**:
- Add SuperTokens middleware (similar to orchestrator)
- Remove `token.json` file usage
- Implement OAuth token exchange endpoint
- Create background worker for per-user mail fetching
- Add user context to all mail storage operations

**New Endpoints**:
```python
POST /api/tokens/exchange
- Exchange Google auth code for refresh token
- Encrypt and store refresh token
- Called once after user login

POST /api/tokens/refresh
- Refresh access token using stored refresh token
- Internal endpoint for background worker
```

### 2.3 Search Service Integration ✅ COMPLETE
**Status**: Fully implemented with user isolation
**Current State**:
- SuperTokens middleware integrated
- All API endpoints require authentication
- Qdrant backend filters by user_id
- Index operations include user_id filtering

**Verification**: Check `services/search/src/api.py` - already has `verify_session()` dependency

### 2.4 Frontend Authentication Flow
**New Dependencies**:
- supertokens-auth-react
- supertokens-web-js

**Implementation Steps**:
1. **Authentication Provider**: Wrap app with SuperTokens provider
2. **Protected Routes**: Implement route guards for authenticated access
3. **API Integration**: Add authentication headers to all API calls
4. **User Context**: Create React context for user information

**Key Components**:
```typescript
// services/frontend/src/lib/auth/supertokens.ts
import SuperTokens from 'supertokens-web-js';
import Session from 'supertokens-web-js/recipe/session';
import ThirdParty from 'supertokens-web-js/recipe/thirdparty';

SuperTokens.init({
  appInfo: {
    appName: "ClariQ",
    apiDomain: "http://localhost:4000",
    websiteDomain: "http://localhost:3001",
    apiBasePath: "/auth"
  },
  recipeList: [
    ThirdParty.init({
      signInAndUpFeature: {
        providers: [ThirdParty.Google.init()]
      }
    }),
    Session.init()
  ]
});
```

## Phase 3: Security & Service Communication

### 3.1 Service-to-Service Authentication
**Challenge**: How do services verify requests are from authenticated users?

**Solution**: JWT token forwarding with verification
- Frontend includes JWT in Authorization header
- Each service validates JWT with SuperTokens
- Extract user_id from validated token

**Implementation**:
```python
# Shared middleware for all services
from supertokens_python.recipe.session.framework.fastapi import verify_session

@app.middleware("http")
async def verify_user_session(request: Request, call_next):
    try:
        session = await verify_session()(request)
        request.state.user_id = session.get_user_id()
    except Exception:
        raise HTTPException(status_code=401, detail="Unauthorized")
    return await call_next(request)
```

### 3.2 Environment Configuration
**Shared Environment Variables**:
```bash
# All services need these
SUPERTOKENS_CONNECTION_URI=http://supertokens:3567
API_DOMAIN=http://localhost:4000
WEBSITE_DOMAIN=http://localhost:3001

# Service-specific
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
ENCRYPTION_KEY=your_32_byte_encryption_key
```

## Phase 4: Testing & Migration Strategy

### 4.1 Database Migration
**Migration Script**:
```sql
-- 001_add_user_isolation.sql
-- Run this as part of deployment

-- Create users table
CREATE TABLE orchestrator.users (...);

-- Add user_id columns with default for existing data
ALTER TABLE orchestrator.conversations 
ADD COLUMN user_id UUID,
ADD CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES orchestrator.users(id);

-- Handle existing data
-- Option 1: Create default user for all existing data
INSERT INTO orchestrator.users (id, supertokens_id, email) 
VALUES ('00000000-0000-0000-0000-000000000000', 'migration_user', '<EMAIL>');

-- Update all existing records to use default user
UPDATE orchestrator.conversations SET user_id = '00000000-0000-0000-0000-000000000000';
UPDATE orchestrator.conversation_turns SET user_id = '00000000-0000-0000-0000-000000000000';
UPDATE orchestrator.conversation_steps SET user_id = '00000000-0000-0000-0000-000000000000';

-- Make user_id NOT NULL after migration
ALTER TABLE orchestrator.conversations ALTER COLUMN user_id SET NOT NULL;
```

### 4.2 Testing Strategy
**Unit Tests**:
- Test user isolation in database queries
- Test token encryption/decryption
- Test JWT validation

**Integration Tests**:
- End-to-end user registration/login flow
- Cross-service authentication
- Data isolation verification

**Load Tests**:
- Multi-user concurrent access
- Token refresh performance
- Database query performance with user scoping

## Phase 5: Deployment & Rollout

### 5.1 Docker Compose Updates
**New Services**:
```yaml
# Add to services/docker-compose.yaml
services:
  supertokens:
    image: supertokens/supertokens-postgresql:9.2.2
    environment:
      - POSTGRESQL_CONNECTION_URI=************************************/supertokens
    ports:
      - "3567:3567"

  auth:
    build: ./services/auth
    ports:
      - "4000:4000"
    environment:
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - SUPERTOKENS_CONNECTION_URI=http://supertokens:3567
```

### 5.2 Rollout Plan
1. **Phase 1**: Deploy auth service and SuperTokens
2. **Phase 2**: Run database migrations
3. **Phase 3**: Deploy updated services with backward compatibility
4. **Phase 4**: Enable frontend authentication
5. **Phase 5**: Migrate existing data (if any)
6. **Phase 6**: Remove legacy endpoints

## Phase 6: Monitoring & Observability

### 6.1 Authentication Metrics
- Failed login attempts
- Token refresh rates
- User registration counts
- Cross-service authentication latency

### 6.2 Security Monitoring
- Unusual login patterns
- Token abuse detection
- Rate limiting violations
- Failed authentication attempts

## Implementation Checklist

### Week 1: Foundation
- [ ] Set up auth service with SuperTokens
- [ ] Create user database schema
- [ ] Implement token encryption utilities

### Week 2: Service Updates
- [ ] Update orchestrator service with user context
- [ ] Refactor mail connection service
- [ ] Update search service integration

### Week 3: Frontend
- [ ] Implement authentication UI
- [ ] Add protected routes
- [ ] Update API calls with authentication

### Week 4: Testing & Deployment
- [ ] Write comprehensive tests
- [ ] Create migration scripts
- [ ] Deploy to staging environment
- [ ] Performance testing

### Week 5: Production
- [ ] Production deployment
- [ ] Monitor metrics
- [ ] User onboarding

## Risk Mitigation

### Data Loss Prevention
- Full database backup before migration
- Staged rollout with rollback capability
- Data validation scripts

### Performance Impact
- Database query optimization
- Connection pooling configuration
- Caching strategy for user context

### Security Considerations
- Token rotation policies
- Encryption key management
- Audit logging for sensitive operations

## Success Criteria
- [ ] All user data properly isolated
- [ ] Authentication working across all services
- [ ] No data loss during migration
- [ ] Performance within acceptable limits
- [ ] Security audit passed
- [ ] User acceptance testing completed

## Next Steps
1. Review and approve this plan
2. Set up development environment with new auth service
3. Begin Phase 1 implementation
4. Create feature branch for authentication work
5. Schedule database migration window
